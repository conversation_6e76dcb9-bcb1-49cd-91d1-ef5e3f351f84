# TalusAegis Makefile
# eBPF-based ICMP responder

# Project information
PROJECT_NAME := talusaegis
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_DATE := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")

# Go build information
GO_VERSION := $(shell go version | cut -d' ' -f3)
GOOS := linux
CGO_ENABLED := 1

# Build flags
LDFLAGS := -X main.version=$(VERSION) \
           -X main.commit=$(COMMIT) \
           -X main.date=$(BUILD_DATE) \
           -s -w

# Directories
BUILD_DIR := build
BIN_DIR := $(BUILD_DIR)/bin
DOCKER_DIR := $(BUILD_DIR)/docker
SCRIPTS_DIR := $(BUILD_DIR)/scripts

# Binary names
BINARY_AMD64 := $(BIN_DIR)/$(PROJECT_NAME)-linux-amd64
BINARY_ARM64 := $(BIN_DIR)/$(PROJECT_NAME)-linux-arm64

# eBPF requirements
CLANG := clang
LLVM_STRIP := $(shell which llvm-strip 2>/dev/null || which strip 2>/dev/null || echo "strip")

# Default target
.PHONY: all
all: clean deps generate build

# Help target
.PHONY: help
help:
	@echo "TalusAegis Build System"
	@echo "======================="
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Clean, install deps, generate, and build"
	@echo "  build        - Build binaries for all architectures"
	@echo "  build-amd64  - Build binary for linux/amd64"
	@echo "  build-arm64  - Build binary for linux/arm64"
	@echo "  clean        - Clean build artifacts"
	@echo "  deps         - Install dependencies"
	@echo "  generate     - Generate eBPF Go bindings"
	@echo "  test         - Run tests"
	@echo "  test-unit    - Run unit tests"
	@echo "  test-integration - Run integration tests"
	@echo "  lint         - Run linters"
	@echo "  fmt          - Format code"
	@echo "  docker       - Build Docker images"
	@echo "  install      - Install binary to system"
	@echo "  uninstall    - Uninstall binary from system"
	@echo "  check-deps   - Check required dependencies"
	@echo ""

# Check dependencies
.PHONY: check-deps
check-deps:
	@echo "Checking dependencies..."
	@which go >/dev/null || (echo "Go is required but not installed" && exit 1)
	@which $(CLANG) >/dev/null || (echo "Clang is required but not installed" && exit 1)
	@echo "All dependencies are available"

# Install Go dependencies
.PHONY: deps
deps: check-deps
	@echo "Installing Go dependencies..."
	go mod download
	go mod tidy
	@echo "Installing eBPF code generation tools..."
	go install github.com/cilium/ebpf/cmd/bpf2go@latest

# Generate eBPF Go bindings
.PHONY: generate
generate: deps
	@echo "Generating eBPF Go bindings..."
	@if [ "$(shell uname -s)" = "Linux" ]; then \
		cd internal/pkg/ebpf && go generate ./...; \
	else \
		echo "Skipping eBPF generation on non-Linux platform"; \
		$(MAKE) generate-mock; \
	fi

# Generate mock eBPF objects for non-Linux platforms
.PHONY: generate-mock
generate-mock:
	@echo "Generating mock eBPF objects..."
	@cd internal/pkg/ebpf && go run ../../../build/scripts/generate_mock.go

# Build all architectures
.PHONY: build
build: generate
	@if [ "$(shell uname -s)" = "Linux" ]; then \
		$(MAKE) build-amd64 build-arm64; \
	else \
		echo "Building for development on $(shell uname -s)"; \
		$(MAKE) build-dev; \
	fi

# Build for development (current platform)
.PHONY: build-dev
build-dev:
	@echo "Building for development..."
	@mkdir -p $(BUILD_DIR)/bin
	go build -ldflags "$(LDFLAGS)" \
		-o $(BUILD_DIR)/bin/talusaegis-dev \
		./cmd/talusaegis

# Build for linux/amd64
.PHONY: build-amd64
build-amd64: generate
	@echo "Building for linux/amd64..."
	@mkdir -p $(BIN_DIR)
	GOOS=$(GOOS) GOARCH=amd64 CGO_ENABLED=$(CGO_ENABLED) \
		go build -ldflags "$(LDFLAGS)" \
		-o $(BINARY_AMD64) \
		./cmd/$(PROJECT_NAME)

# Build for linux/arm64
.PHONY: build-arm64
build-arm64: generate
	@echo "Building for linux/arm64..."
	@mkdir -p $(BIN_DIR)
	GOOS=$(GOOS) GOARCH=arm64 CGO_ENABLED=$(CGO_ENABLED) \
		go build -ldflags "$(LDFLAGS)" \
		-o $(BINARY_ARM64) \
		./cmd/$(PROJECT_NAME)

# Clean build artifacts
.PHONY: clean
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR)
	rm -f internal/pkg/ebpf/*_bpfel.go
	rm -f internal/pkg/ebpf/*_bpfeb.go
	rm -f internal/pkg/ebpf/*_bpfel.o
	rm -f internal/pkg/ebpf/*_bpfeb.o
	go clean -cache

# Run tests
.PHONY: test
test: test-unit test-integration

# Run unit tests
.PHONY: test-unit
test-unit:
	@echo "Running unit tests..."
	go test -v -race -coverprofile=coverage.out ./...

# Run integration tests
.PHONY: test-integration
test-integration:
	@echo "Running integration tests..."
	go test -v -tags=integration ./test/integration/...

# Run linters
.PHONY: lint
lint:
	@echo "Running linters..."
	@which golangci-lint >/dev/null || (echo "Installing golangci-lint..." && \
		curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(shell go env GOPATH)/bin)
	golangci-lint run

# Format code
.PHONY: fmt
fmt:
	@echo "Formatting code..."
	go fmt ./...
	goimports -w .

# Build Docker images
.PHONY: docker
docker: build
	@echo "Building Docker images..."
	@mkdir -p $(DOCKER_DIR)
	docker build -t $(PROJECT_NAME):$(VERSION) -f build/docker/Dockerfile .
	docker build -t $(PROJECT_NAME):latest -f build/docker/Dockerfile .

# Install binary to system
.PHONY: install
install: build-amd64
	@echo "Installing $(PROJECT_NAME) to /usr/local/bin..."
	sudo cp $(BINARY_AMD64) /usr/local/bin/$(PROJECT_NAME)
	sudo chmod +x /usr/local/bin/$(PROJECT_NAME)

# Uninstall binary from system
.PHONY: uninstall
uninstall:
	@echo "Uninstalling $(PROJECT_NAME) from /usr/local/bin..."
	sudo rm -f /usr/local/bin/$(PROJECT_NAME)

# Development targets
.PHONY: dev
dev: generate
	@echo "Starting development build..."
	go run ./cmd/$(PROJECT_NAME) run --log-level debug --log-format console

# Quick build for development
.PHONY: dev-build
dev-build: generate
	@echo "Building development binary..."
	@mkdir -p $(BIN_DIR)
	go build -ldflags "$(LDFLAGS)" -o $(BIN_DIR)/$(PROJECT_NAME) ./cmd/$(PROJECT_NAME)

# Show build information
.PHONY: info
info:
	@echo "Build Information"
	@echo "================="
	@echo "Project: $(PROJECT_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Commit:  $(COMMIT)"
	@echo "Date:    $(BUILD_DATE)"
	@echo "Go:      $(GO_VERSION)"
	@echo "GOOS:    $(GOOS)"
	@echo "CGO:     $(CGO_ENABLED)"

# Create release archives
.PHONY: release
release: build
	@echo "Creating release archives..."
	@mkdir -p $(BUILD_DIR)/release
	cd $(BIN_DIR) && tar -czf ../release/$(PROJECT_NAME)-$(VERSION)-linux-amd64.tar.gz $(PROJECT_NAME)-linux-amd64
	cd $(BIN_DIR) && tar -czf ../release/$(PROJECT_NAME)-$(VERSION)-linux-arm64.tar.gz $(PROJECT_NAME)-linux-arm64
	@echo "Release archives created in $(BUILD_DIR)/release/"

# Benchmark tests
.PHONY: bench
bench:
	@echo "Running benchmarks..."
	go test -bench=. -benchmem ./...

# Security scan
.PHONY: security
security:
	@echo "Running security scan..."
	@which gosec >/dev/null || (echo "Installing gosec..." && go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest)
	gosec ./...

.PHONY: mod-update
mod-update:
	@echo "Updating Go modules..."
	go get -u ./...
	go mod tidy
