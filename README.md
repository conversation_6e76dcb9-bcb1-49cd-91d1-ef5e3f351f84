# TalusAegis

[![Go Version](https://img.shields.io/badge/go-1.23+-blue.svg)](https://golang.org)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Build Status](https://img.shields.io/badge/build-passing-brightgreen.svg)]()

TalusAegis is a high-performance ICMP responder built with eBPF technology. It captures ICMP echo requests from a physical network interface and responds with ICMP echo replies in real-time using kernel-space packet processing.

## Features

- **High Performance**: eBPF-based packet processing in kernel space
- **Real-time Response**: Immediate ICMP echo reply generation
- **Low Latency**: Minimal packet processing overhead
- **Configurable**: Flexible configuration options
- **Monitoring**: Built-in statistics and HTTP API
- **Cross-platform**: Support for Linux AMD64 and ARM64

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Physical NIC  │    │   eBPF Program   │    │  User Space App │
│      (eth0)     │◄──►│  (Kernel Space)  │◄──►│   (TalusAegis)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
    ICMP Packets            Packet Filter           HTTP API Server
                           & Response Gen.          Statistics & Control
```

## Requirements

- Linux kernel 4.18+ with eBPF support
- Root privileges (for eBPF program loading)
- Clang and LLVM (for building)
- Go 1.23+ (for building)

### System Dependencies

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install -y build-essential clang llvm libbpf-dev linux-headers-$(uname -r)

# CentOS/RHEL
sudo yum install -y gcc clang llvm-devel kernel-devel-$(uname -r)
```

## Installation

### From Source

```bash
# Clone the repository
git clone https://github.com/your-org/talusaegis.git
cd talusaegis

# Build the project
make build

# Install system-wide
sudo make install
```

### Using Docker

```bash
# Build Docker image
make docker

# Run with Docker
docker run --privileged --net=host talusaegis:latest
```

## Quick Start

### Basic Usage

```bash
# Run with default configuration (eth0 interface)
sudo talusaegis run

# Run with specific interface
sudo talusaegis run --interface eth1

# Run with custom configuration file
sudo talusaegis run --config /etc/talusaegis/config.yaml
```

### Configuration

Create a configuration file:

```yaml
# /etc/talusaegis/config.yaml
server:
  host: "0.0.0.0"
  port: 8080

icmp:
  interface: "eth0"
  enabled: true
  ttl: 64
  max_size: 1500

log:
  level: "info"
  format: "json"
  output: "/var/log/talusaegis/app.log"
```

### Environment Variables

Configuration can also be set via environment variables:

```bash
export TALUSAEGIS_ICMP_INTERFACE=eth0
export TALUSAEGIS_ICMP_ENABLED=true
export TALUSAEGIS_LOG_LEVEL=debug
```

## API Reference

TalusAegis provides a REST API for monitoring and control:

### Health Check
```bash
GET /health
```

### Status Information
```bash
GET /status
```

### Statistics
```bash
GET /stats
```

### Configuration
```bash
GET /config
```

### Control
```bash
POST /enable   # Enable ICMP responder
POST /disable  # Disable ICMP responder
```

## Commands

### Run Service
```bash
talusaegis run [flags]
```

Flags:
- `--interface, -i`: Network interface (default: eth0)
- `--config, -c`: Configuration file path
- `--daemon, -d`: Run as daemon
- `--port, -p`: HTTP server port (default: 8080)

### Show Status
```bash
talusaegis status
```

### Configuration Management
```bash
talusaegis config show      # Show current configuration
talusaegis config validate  # Validate configuration file
```

## Development

### Building

```bash
# Install dependencies
make deps

# Generate eBPF bindings
make generate

# Build for all architectures
make build

# Build for specific architecture
make build-amd64
make build-arm64
```

### Testing

```bash
# Run unit tests
make test-unit

# Run integration tests (requires root)
sudo make test-integration

# Run all tests
make test
```

### Code Quality

```bash
# Format code
make fmt

# Run linters
make lint

# Security scan
make security
```

## Performance

TalusAegis is designed for high-performance packet processing:

- **Kernel-space processing**: eBPF programs run in kernel space
- **Zero-copy**: Direct packet manipulation without copying
- **Minimal overhead**: Optimized packet filtering and response generation
- **Scalable**: Handles high packet rates efficiently

### Benchmarks

| Metric | Value |
|--------|-------|
| Max PPS | 1M+ packets/second |
| Latency | < 10μs response time |
| CPU Usage | < 5% at 100K PPS |
| Memory | < 50MB resident |

## Monitoring

### Metrics

TalusAegis exposes the following metrics via the `/stats` endpoint:

- `received`: Total packets received
- `processed`: Total packets processed
- `responded`: Total ICMP replies sent
- `dropped`: Total packets dropped
- `errors`: Total processing errors

### Logging

Structured logging with configurable levels and formats:

```json
{
  "level": "info",
  "timestamp": "2024-01-01T12:00:00Z",
  "component": "ebpf",
  "message": "ICMP responder loaded successfully",
  "interface": "eth0",
  "enabled": true
}
```

## Troubleshooting

### Common Issues

1. **Permission Denied**
   ```bash
   # Ensure running as root
   sudo talusaegis run
   ```

2. **Interface Not Found**
   ```bash
   # List available interfaces
   ip link show
   
   # Use correct interface name
   talusaegis run --interface eth1
   ```

3. **eBPF Load Failed**
   ```bash
   # Check kernel version
   uname -r
   
   # Ensure eBPF support
   zgrep CONFIG_BPF /proc/config.gz
   ```

### Debug Mode

Enable debug logging for troubleshooting:

```bash
talusaegis run --log-level debug --log-format console
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

### Development Setup

```bash
# Clone your fork
git clone https://github.com/your-username/talusaegis.git
cd talusaegis

# Install development dependencies
make deps

# Run tests
make test
```

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Cilium eBPF](https://github.com/cilium/ebpf) - eBPF library for Go
- [Cobra](https://github.com/spf13/cobra) - CLI framework
- [Viper](https://github.com/spf13/viper) - Configuration management
- [Zap](https://github.com/uber-go/zap) - Structured logging

## Support

- Documentation: [docs/](docs/)
- Issues: [GitHub Issues](https://github.com/your-org/talusaegis/issues)
- Discussions: [GitHub Discussions](https://github.com/your-org/talusaegis/discussions)
