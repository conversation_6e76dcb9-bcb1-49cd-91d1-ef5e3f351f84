//go:build integration

package integration

import (
	"context"
	"net/http"
	"os"
	"testing"
	"time"

	"go.uber.org/zap"

	"talusaegis/internal/app"
	"talusaegis/internal/pkg/config"
	"talusaegis/internal/pkg/logger"
)

func TestICMPResponderIntegration(t *testing.T) {
	// Skip if not running as root (required for eBPF)
	if os.Geteuid() != 0 {
		t.Skip("Integration tests require root privileges for eBPF")
	}

	// Create test logger
	logConfig := &logger.Config{
		Level:  "debug",
		Format: "console",
		Output: "stdout",
	}
	log, err := logger.New(logConfig)
	if err != nil {
		t.Fatalf("Failed to create logger: %v", err)
	}

	// Create test configuration
	cfg := &config.Config{
		Server: config.ServerConfig{
			Host:         "127.0.0.1",
			Port:         18080, // Use different port for testing
			ReadTimeout:  10,
			WriteTimeout: 10,
		},
		ICMP: config.ICMPConfig{
			Interface: "lo", // Use loopback interface for testing
			Enabled:   true,
			TTL:       64,
			MaxSize:   1500,
		},
		Log: config.LogConfig{
			Level:  "debug",
			Format: "console",
			Output: "stdout",
		},
	}

	// Create application
	application, err := app.New(log, cfg)
	if err != nil {
		t.Fatalf("Failed to create application: %v", err)
	}

	// Start application in background
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	errChan := make(chan error, 1)
	go func() {
		errChan <- application.Run(ctx)
	}()

	// Wait for application to start
	time.Sleep(2 * time.Second)

	// Test health endpoint
	t.Run("HealthCheck", func(t *testing.T) {
		resp, err := http.Get("http://127.0.0.1:18080/health")
		if err != nil {
			t.Fatalf("Failed to call health endpoint: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	})

	// Test status endpoint
	t.Run("StatusCheck", func(t *testing.T) {
		resp, err := http.Get("http://127.0.0.1:18080/status")
		if err != nil {
			t.Fatalf("Failed to call status endpoint: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	})

	// Test statistics endpoint
	t.Run("StatsCheck", func(t *testing.T) {
		resp, err := http.Get("http://127.0.0.1:18080/stats")
		if err != nil {
			t.Fatalf("Failed to call stats endpoint: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	})

	// Test configuration endpoint
	t.Run("ConfigCheck", func(t *testing.T) {
		resp, err := http.Get("http://127.0.0.1:18080/config")
		if err != nil {
			t.Fatalf("Failed to call config endpoint: %v", err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200, got %d", resp.StatusCode)
		}
	})

	// Test enable/disable endpoints
	t.Run("EnableDisable", func(t *testing.T) {
		// Test disable
		resp, err := http.Post("http://127.0.0.1:18080/disable", "application/json", nil)
		if err != nil {
			t.Fatalf("Failed to call disable endpoint: %v", err)
		}
		resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200 for disable, got %d", resp.StatusCode)
		}

		// Test enable
		resp, err = http.Post("http://127.0.0.1:18080/enable", "application/json", nil)
		if err != nil {
			t.Fatalf("Failed to call enable endpoint: %v", err)
		}
		resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			t.Errorf("Expected status 200 for enable, got %d", resp.StatusCode)
		}
	})

	// Stop application
	cancel()

	// Wait for application to stop
	select {
	case err := <-errChan:
		if err != nil {
			t.Errorf("Application error: %v", err)
		}
	case <-time.After(5 * time.Second):
		t.Error("Application did not stop within timeout")
	}
}

func TestConfigurationLoading(t *testing.T) {
	// Create temporary config file
	tmpDir := t.TempDir()
	configFile := tmpDir + "/test_config.yaml"

	configContent := `
server:
  host: "0.0.0.0"
  port: 8080
icmp:
  interface: "eth0"
  enabled: true
  ttl: 64
log:
  level: "info"
  format: "json"
`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write test config: %v", err)
	}

	// Test configuration loading
	log := zap.NewNop()
	manager := config.NewManager(log)

	if err := manager.Load(configFile); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	cfg := manager.Get()
	if cfg == nil {
		t.Fatal("Expected config to be loaded")
	}

	// Verify configuration values
	if cfg.Server.Host != "0.0.0.0" {
		t.Errorf("Expected host '0.0.0.0', got '%s'", cfg.Server.Host)
	}

	if cfg.Server.Port != 8080 {
		t.Errorf("Expected port 8080, got %d", cfg.Server.Port)
	}

	if cfg.ICMP.Interface != "eth0" {
		t.Errorf("Expected interface 'eth0', got '%s'", cfg.ICMP.Interface)
	}

	if !cfg.ICMP.Enabled {
		t.Error("Expected ICMP to be enabled")
	}

	if cfg.Log.Level != "info" {
		t.Errorf("Expected log level 'info', got '%s'", cfg.Log.Level)
	}
}
