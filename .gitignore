# TalusAegis .gitignore
# ====================

# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Build artifacts
/build/bin/
/build/tmp/
/build/dist/
/build/release/

# eBPF generated files
*_bpfel.go
*_bpfeb.go
*_bpfel.o
*_bpfeb.o
*.bpf.o
vmlinux.h

# Coverage reports
coverage.out
coverage.html
*.cover
*.coverprofile

# Logs
*.log
logs/
*.log.*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Configuration files (keep templates)
config.yaml
config.json
config.toml
!configs/config.yaml
!configs/*.example.*
!configs/*.template.*

# Environment variables
.env
.env.local
.env.*.local

# IDE and Editor files
# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# GoLand/IntelliJ
.idea/
*.iml
*.ipr
*.iws

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Sublime Text
*.sublime-project
*.sublime-workspace

# Atom
.atom/

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.orig
*.rej

# Package files
*.tar.gz
*.tgz
*.zip
*.rar
*.7z
*.deb
*.rpm

# Docker
.dockerignore
Dockerfile.tmp
docker-compose.override.yml

# Kubernetes
*.kubeconfig
kustomization.yaml.tmp

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
*.tfvars
!*.tfvars.example

# Ansible
*.retry

# Vagrant
.vagrant/

# Local development
.local/
local/
tmp/
temp/

# Performance and profiling
*.prof
*.pprof
*.trace
cpu.prof
mem.prof
block.prof
mutex.prof

# Security
*.key
*.pem
*.crt
*.csr
*.p12
*.pfx
secrets/
.secrets/

# Database
*.db
*.sqlite
*.sqlite3

# Cache directories
.cache/
cache/
node_modules/
.npm/
.yarn/

# OS generated files
.fseventsd/
.Spotlight-V100/
.Trashes/

# Project specific
# Add any project-specific ignore patterns here
debug
debug.test
main
*.debug

# Test artifacts
testdata/output/
test-results/
junit.xml

# Documentation build
docs/_build/
docs/site/
site/

# Backup files
*.bak
*.backup
*~

# Lock files (keep package manager lock files)
# Uncomment if you want to ignore lock files
# go.sum
# package-lock.json
# yarn.lock

# Local scripts and notes
scripts/local/
notes.md
TODO.md
NOTES.md

# Benchmarks
*.bench
benchmarks/

# Generated documentation
docs/api/
docs/generated/

# Local configuration overrides
local.yaml
local.json
local.toml
development.yaml
development.json

# Certificates and keys
certs/
keys/
ssl/

# Monitoring and metrics
metrics/
prometheus/
grafana/

# Helm charts (if using)
charts/*/charts/
charts/*/requirements.lock

# Ignore but keep directory structure
!.gitkeep
!.keep
