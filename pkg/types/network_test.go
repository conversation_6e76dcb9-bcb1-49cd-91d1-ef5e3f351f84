package types

import (
	"net"
	"testing"
	"time"
)

func TestICMPPacket(t *testing.T) {
	packet := ICMPPacket{
		Type:     8, // Echo Request
		Code:     0,
		Checksum: 0x1234,
		ID:       0x5678,
		Sequence: 1,
		Data:     []byte("test data"),
	}

	if packet.Type != 8 {
		t.<PERSON><PERSON><PERSON>("Expected Type 8, got %d", packet.Type)
	}

	if packet.Code != 0 {
		t.<PERSON><PERSON>("Expected Code 0, got %d", packet.Code)
	}

	if packet.ID != 0x5678 {
		t.<PERSON><PERSON><PERSON>("Expected ID 0x5678, got 0x%x", packet.ID)
	}

	if string(packet.Data) != "test data" {
		t.<PERSON>("Expected Data 'test data', got '%s'", string(packet.Data))
	}
}

func TestEthernetHeader(t *testing.T) {
	header := EthernetHeader{
		DstMAC: [6]byte{0x00, 0x11, 0x22, 0x33, 0x44, 0x55},
		SrcMAC: [6]byte{0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff},
		Type:   0x0800, // IPv4
	}

	expectedDst := [6]byte{0x00, 0x11, 0x22, 0x33, 0x44, 0x55}
	if header.DstMAC != expectedDst {
		t.Errorf("Expected DstMAC %v, got %v", expectedDst, header.DstMAC)
	}

	expectedSrc := [6]byte{0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff}
	if header.SrcMAC != expectedSrc {
		t.Errorf("Expected SrcMAC %v, got %v", expectedSrc, header.SrcMAC)
	}

	if header.Type != 0x0800 {
		t.Errorf("Expected Type 0x0800, got 0x%x", header.Type)
	}
}

func TestIPHeader(t *testing.T) {
	srcIP := net.ParseIP("***********")
	dstIP := net.ParseIP("***********")

	header := IPHeader{
		Version:  4,
		IHL:      5,
		TOS:      0,
		Length:   84,
		ID:       0x1234,
		Flags:    0x4000, // Don't Fragment
		TTL:      64,
		Protocol: 1, // ICMP
		Checksum: 0,
		SrcIP:    srcIP,
		DstIP:    dstIP,
	}

	if header.Version != 4 {
		t.Errorf("Expected Version 4, got %d", header.Version)
	}

	if header.Protocol != 1 {
		t.Errorf("Expected Protocol 1 (ICMP), got %d", header.Protocol)
	}

	if !header.SrcIP.Equal(srcIP) {
		t.Errorf("Expected SrcIP %v, got %v", srcIP, header.SrcIP)
	}

	if !header.DstIP.Equal(dstIP) {
		t.Errorf("Expected DstIP %v, got %v", dstIP, header.DstIP)
	}
}

func TestNetworkPacket(t *testing.T) {
	now := time.Now()
	srcIP := net.ParseIP("***********")
	dstIP := net.ParseIP("***********")

	packet := NetworkPacket{
		Timestamp: now,
		Interface: "eth0",
		Ethernet: EthernetHeader{
			DstMAC: [6]byte{0x00, 0x11, 0x22, 0x33, 0x44, 0x55},
			SrcMAC: [6]byte{0xaa, 0xbb, 0xcc, 0xdd, 0xee, 0xff},
			Type:   0x0800,
		},
		IP: IPHeader{
			Version:  4,
			IHL:      5,
			Protocol: 1,
			SrcIP:    srcIP,
			DstIP:    dstIP,
		},
		ICMP: ICMPPacket{
			Type: 8,
			Code: 0,
			ID:   0x1234,
		},
		RawData: []byte("raw packet data"),
	}

	if packet.Interface != "eth0" {
		t.Errorf("Expected Interface 'eth0', got '%s'", packet.Interface)
	}

	if !packet.Timestamp.Equal(now) {
		t.Errorf("Expected Timestamp %v, got %v", now, packet.Timestamp)
	}

	if packet.IP.Protocol != 1 {
		t.Errorf("Expected IP Protocol 1, got %d", packet.IP.Protocol)
	}

	if packet.ICMP.Type != 8 {
		t.Errorf("Expected ICMP Type 8, got %d", packet.ICMP.Type)
	}
}

func TestPacketStats(t *testing.T) {
	now := time.Now()
	stats := PacketStats{
		Received:    100,
		Processed:   95,
		Responded:   90,
		Dropped:     5,
		Errors:      0,
		LastUpdated: now,
	}

	if stats.Received != 100 {
		t.Errorf("Expected Received 100, got %d", stats.Received)
	}

	if stats.Processed != 95 {
		t.Errorf("Expected Processed 95, got %d", stats.Processed)
	}

	if stats.Responded != 90 {
		t.Errorf("Expected Responded 90, got %d", stats.Responded)
	}

	if stats.Dropped != 5 {
		t.Errorf("Expected Dropped 5, got %d", stats.Dropped)
	}

	if stats.Errors != 0 {
		t.Errorf("Expected Errors 0, got %d", stats.Errors)
	}

	if !stats.LastUpdated.Equal(now) {
		t.Errorf("Expected LastUpdated %v, got %v", now, stats.LastUpdated)
	}
}

func TestNetworkInterface(t *testing.T) {
	mac, _ := net.ParseMAC("00:11:22:33:44:55")
	_, ipnet, _ := net.ParseCIDR("***********/24")

	iface := NetworkInterface{
		Name:         "eth0",
		Index:        2,
		MTU:          1500,
		HardwareAddr: mac,
		Addresses:    []net.Addr{ipnet},
		Flags:        net.FlagUp | net.FlagBroadcast,
	}

	if iface.Name != "eth0" {
		t.Errorf("Expected Name 'eth0', got '%s'", iface.Name)
	}

	if iface.Index != 2 {
		t.Errorf("Expected Index 2, got %d", iface.Index)
	}

	if iface.MTU != 1500 {
		t.Errorf("Expected MTU 1500, got %d", iface.MTU)
	}

	if iface.HardwareAddr.String() != mac.String() {
		t.Errorf("Expected HardwareAddr %s, got %s", mac.String(), iface.HardwareAddr.String())
	}

	if len(iface.Addresses) != 1 {
		t.Errorf("Expected 1 address, got %d", len(iface.Addresses))
	}

	if iface.Flags&net.FlagUp == 0 {
		t.Error("Expected interface to have FlagUp set")
	}
}
