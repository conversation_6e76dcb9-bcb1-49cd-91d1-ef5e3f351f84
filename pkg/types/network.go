package types

import (
	"net"
	"time"
)

// ICMPPacket represents an ICMP packet structure
type ICMPPacket struct {
	Type     uint8  `json:"type"`
	Code     uint8  `json:"code"`
	Checksum uint16 `json:"checksum"`
	ID       uint16 `json:"id"`
	Sequence uint16 `json:"sequence"`
	Data     []byte `json:"data"`
}

// EthernetHeader represents an Ethernet frame header
type EthernetHeader struct {
	DstMAC [6]byte `json:"dst_mac"`
	SrcMAC [6]byte `json:"src_mac"`
	Type   uint16  `json:"type"`
}

// IPHeader represents an IPv4 header
type IPHeader struct {
	Version    uint8  `json:"version"`
	IHL        uint8  `json:"ihl"`
	TOS        uint8  `json:"tos"`
	Length     uint16 `json:"length"`
	ID         uint16 `json:"id"`
	Flags      uint16 `json:"flags"`
	TTL        uint8  `json:"ttl"`
	Protocol   uint8  `json:"protocol"`
	Checksum   uint16 `json:"checksum"`
	SrcIP      net.IP `json:"src_ip"`
	DstIP      net.IP `json:"dst_ip"`
}

// NetworkPacket represents a complete network packet
type NetworkPacket struct {
	Timestamp time.Time       `json:"timestamp"`
	Interface string          `json:"interface"`
	Ethernet  EthernetHeader  `json:"ethernet"`
	IP        IPHeader        `json:"ip"`
	ICMP      ICMPPacket      `json:"icmp"`
	RawData   []byte          `json:"raw_data"`
}

// PacketStats represents packet processing statistics
type PacketStats struct {
	Received    uint64 `json:"received"`
	Processed   uint64 `json:"processed"`
	Responded   uint64 `json:"responded"`
	Dropped     uint64 `json:"dropped"`
	Errors      uint64 `json:"errors"`
	LastUpdated time.Time `json:"last_updated"`
}

// NetworkInterface represents a network interface configuration
type NetworkInterface struct {
	Name       string `json:"name"`
	Index      int    `json:"index"`
	MTU        int    `json:"mtu"`
	HardwareAddr net.HardwareAddr `json:"hardware_addr"`
	Addresses  []net.Addr `json:"addresses"`
	Flags      net.Flags  `json:"flags"`
}
