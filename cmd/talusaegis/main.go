package main

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"os/signal"
	"strings"
	"syscall"

	"github.com/spf13/cobra"
	"go.uber.org/zap"

	"talusaegis/internal/app"
	"talusaegis/internal/pkg/config"
	"talusaegis/internal/pkg/logger"
)

var (
	configFile string
	logLevel   string
	logFormat  string
	version    = "dev"
	commit     = "unknown"
	date       = "unknown"
)

// rootCmd represents the base command when called without any subcommands
var rootCmd = &cobra.Command{
	Use:   "talusaegis",
	Short: "TalusAegis - eBPF-based ICMP responder",
	Long: `TalusAegis is a high-performance ICMP responder built with eBPF technology.
It captures ICMP echo requests from a physical network interface and responds
with ICMP echo replies in real-time using kernel-space packet processing.`,
	Version: fmt.Sprintf("%s (commit: %s, built: %s)", version, commit, date),
}

// runCmd represents the run command
var runCmd = &cobra.Command{
	Use:   "run",
	Short: "Run the ICMP responder service",
	Long: `Start the TalusAegis ICMP responder service.
This will load the eBPF program and begin responding to ICMP echo requests.`,
	RunE: runService,
}

// statusCmd represents the status command
var statusCmd = &cobra.Command{
	Use:   "status",
	Short: "Show service status and statistics",
	Long:  `Display the current status of the ICMP responder and packet processing statistics.`,
	RunE:  showStatus,
}

// configCmd represents the config command
var configCmd = &cobra.Command{
	Use:   "config",
	Short: "Configuration management commands",
	Long:  `Commands for managing TalusAegis configuration.`,
}

// configShowCmd shows current configuration
var configShowCmd = &cobra.Command{
	Use:   "show",
	Short: "Show current configuration",
	Long:  `Display the current configuration settings.`,
	RunE:  showConfig,
}

// configValidateCmd validates configuration
var configValidateCmd = &cobra.Command{
	Use:   "validate",
	Short: "Validate configuration file",
	Long:  `Validate the configuration file for syntax and semantic errors.`,
	RunE:  validateConfig,
}

func init() {
	// Global flags
	rootCmd.PersistentFlags().StringVarP(&configFile, "config", "c", "", "config file path")
	rootCmd.PersistentFlags().StringVar(&logLevel, "log-level", "info", "log level (debug, info, warn, error, fatal)")
	rootCmd.PersistentFlags().StringVar(&logFormat, "log-format", "console", "log format (json, console)")

	// Add subcommands
	rootCmd.AddCommand(runCmd)
	rootCmd.AddCommand(statusCmd)
	rootCmd.AddCommand(configCmd)

	// Config subcommands
	configCmd.AddCommand(configShowCmd)
	configCmd.AddCommand(configValidateCmd)

	// Run command flags
	runCmd.Flags().StringP("interface", "i", "eth0", "network interface to attach to")
	runCmd.Flags().BoolP("daemon", "d", false, "run as daemon")
	runCmd.Flags().IntP("port", "p", 8081, "HTTP server port for status API")
	runCmd.Flags().Bool("auto-reload", true, "automatically reload eBPF program on startup if already attached")
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}
}

func runService(cmd *cobra.Command, args []string) error {
	// Create logger
	logConfig := &logger.Config{
		Level:  logLevel,
		Format: logFormat,
		Output: "stdout",
	}
	log, err := logger.New(logConfig)
	if err != nil {
		return fmt.Errorf("failed to create logger: %w", err)
	}
	defer logger.Sync(log)

	log.Info("Starting TalusAegis ICMP responder",
		zap.String("version", version),
		zap.String("commit", commit),
		zap.String("build_date", date))

	// Load configuration
	configManager := config.NewManager(log)
	if err := configManager.Load(configFile); err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	cfg := configManager.Get()
	log.Info("Configuration loaded", zap.Any("config", cfg))

	// Override interface from command line if provided
	if iface, _ := cmd.Flags().GetString("interface"); iface != "eth0" {
		cfg.ICMP.Interface = iface
	}

	// Override port from command line if provided
	if port, _ := cmd.Flags().GetInt("port"); port != 8080 {
		cfg.Server.Port = port
	}

	// Handle auto-reload if requested
	if autoReload, _ := cmd.Flags().GetBool("auto-reload"); autoReload {
		log.Info("Auto-reload enabled, checking for existing eBPF program")
		if err := handleAutoReload(log, cfg.ICMP.Interface); err != nil {
			log.Warn("Auto-reload failed", zap.Error(err))
		}
	}

	// Create application
	application, err := app.New(log, cfg)
	if err != nil {
		return fmt.Errorf("failed to create application: %w", err)
	}

	// Setup signal handling
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		sig := <-sigChan
		log.Info("Received signal, shutting down", zap.String("signal", sig.String()))
		cancel()
	}()

	// Run application
	if err := application.Run(ctx); err != nil {
		return fmt.Errorf("application error: %w", err)
	}

	log.Info("TalusAegis ICMP responder stopped")
	return nil
}

func showStatus(cmd *cobra.Command, args []string) error {
	// Create logger
	logConfig := &logger.Config{
		Level:  logLevel,
		Format: logFormat,
		Output: "stdout",
	}
	log, err := logger.New(logConfig)
	if err != nil {
		return fmt.Errorf("failed to create logger: %w", err)
	}
	defer logger.Sync(log)

	// Load configuration
	configManager := config.NewManager(log)
	if err := configManager.Load(configFile); err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	cfg := configManager.Get()

	// TODO: Implement status checking logic
	fmt.Printf("TalusAegis Status\n")
	fmt.Printf("=================\n")
	fmt.Printf("Version: %s\n", version)
	fmt.Printf("Interface: %s\n", cfg.ICMP.Interface)
	fmt.Printf("Enabled: %t\n", cfg.ICMP.Enabled)
	fmt.Printf("TTL: %d\n", cfg.ICMP.TTL)
	fmt.Printf("Max Size: %d\n", cfg.ICMP.MaxSize)

	return nil
}

func showConfig(cmd *cobra.Command, args []string) error {
	// Create logger
	logConfig := &logger.Config{
		Level:  logLevel,
		Format: logFormat,
		Output: "stdout",
	}
	log, err := logger.New(logConfig)
	if err != nil {
		return fmt.Errorf("failed to create logger: %w", err)
	}
	defer logger.Sync(log)

	// Load configuration
	configManager := config.NewManager(log)
	if err := configManager.Load(configFile); err != nil {
		return fmt.Errorf("failed to load configuration: %w", err)
	}

	cfg := configManager.Get()

	fmt.Printf("Current Configuration:\n")
	fmt.Printf("=====================\n")
	fmt.Printf("Server:\n")
	fmt.Printf("  Host: %s\n", cfg.Server.Host)
	fmt.Printf("  Port: %d\n", cfg.Server.Port)
	fmt.Printf("  Read Timeout: %d\n", cfg.Server.ReadTimeout)
	fmt.Printf("  Write Timeout: %d\n", cfg.Server.WriteTimeout)
	fmt.Printf("\nICMP:\n")
	fmt.Printf("  Interface: %s\n", cfg.ICMP.Interface)
	fmt.Printf("  Enabled: %t\n", cfg.ICMP.Enabled)
	fmt.Printf("  TTL: %d\n", cfg.ICMP.TTL)
	fmt.Printf("  Max Size: %d\n", cfg.ICMP.MaxSize)
	fmt.Printf("  Debug: %t\n", cfg.ICMP.Debug)
	fmt.Printf("  Use Source IP: %t\n", cfg.ICMP.UseSourceIP)
	fmt.Printf("\nLog:\n")
	fmt.Printf("  Level: %s\n", cfg.Log.Level)
	fmt.Printf("  Format: %s\n", cfg.Log.Format)
	fmt.Printf("  Output: %s\n", cfg.Log.Output)

	return nil
}

func validateConfig(cmd *cobra.Command, args []string) error {
	// Create logger
	logConfig := &logger.Config{
		Level:  logLevel,
		Format: logFormat,
		Output: "stdout",
	}
	log, err := logger.New(logConfig)
	if err != nil {
		return fmt.Errorf("failed to create logger: %w", err)
	}
	defer logger.Sync(log)

	// Load and validate configuration
	configManager := config.NewManager(log)
	if err := configManager.Load(configFile); err != nil {
		fmt.Printf("Configuration validation failed: %v\n", err)
		return err
	}

	fmt.Printf("Configuration is valid\n")
	return nil
}

// handleAutoReload checks for existing eBPF program and removes it if found
func handleAutoReload(log *zap.Logger, interfaceName string) error {
	// Check if there's an existing XDP program attached
	cmd := exec.Command("ip", "link", "show", interfaceName)
	output, err := cmd.Output()
	if err != nil {
		return fmt.Errorf("failed to check interface %s: %w", interfaceName, err)
	}

	// Check if XDP program is attached
	if strings.Contains(string(output), "prog/xdp") {
		log.Info("Found existing XDP program, removing it", zap.String("interface", interfaceName))

		// Remove existing XDP program
		removeCmd := exec.Command("ip", "link", "set", "dev", interfaceName, "xdpgeneric", "off")
		if err := removeCmd.Run(); err != nil {
			return fmt.Errorf("failed to remove existing XDP program from %s: %w", interfaceName, err)
		}

		log.Info("Successfully removed existing XDP program", zap.String("interface", interfaceName))
	} else {
		log.Info("No existing XDP program found", zap.String("interface", interfaceName))
	}

	return nil
}
