#!/bin/bash

# Remote build script for TalusAegis on Linux servers
# This script should be run on the remote Linux server

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on Linux
check_platform() {
    if [ "$(uname -s)" != "Linux" ]; then
        print_error "This script must be run on a Linux system"
        exit 1
    fi
    
    print_info "Running on Linux: $(uname -r)"
}

# Check and install dependencies
install_dependencies() {
    print_info "Checking and installing dependencies..."
    
    # Detect package manager and install dependencies
    if command -v apt-get >/dev/null 2>&1; then
        print_info "Detected Debian/Ubuntu system"
        
        # Update package list
        print_info "Updating package list..."
        apt-get update -qq
        
        # Install required packages
        print_info "Installing eBPF development packages..."
        apt-get install -y \
            libbpf-dev \
            linux-headers-$(uname -r) \
            clang \
            llvm \
            build-essential \
            pkg-config \
            wget \
            curl

        # Install Go if not present
        if ! command -v go >/dev/null 2>&1; then
            print_info "Installing Go..."
            GO_VERSION="1.23.0"
            wget -q "https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz" -O /tmp/go.tar.gz
            rm -rf /usr/local/go
            tar -C /usr/local -xzf /tmp/go.tar.gz
            rm /tmp/go.tar.gz

            # Add Go to PATH
            if ! grep -q "/usr/local/go/bin" /etc/profile; then
                echo 'export PATH=$PATH:/usr/local/go/bin' >> /etc/profile
            fi
            export PATH=$PATH:/usr/local/go/bin

            print_success "Go ${GO_VERSION} installed successfully"
        fi
            
    elif command -v yum >/dev/null 2>&1; then
        print_info "Detected RHEL/CentOS system"
        yum install -y \
            libbpf-devel \
            kernel-devel \
            clang \
            llvm \
            gcc \
            make
            
    elif command -v dnf >/dev/null 2>&1; then
        print_info "Detected Fedora system"
        dnf install -y \
            libbpf-devel \
            kernel-devel \
            clang \
            llvm \
            gcc \
            make
            
    elif command -v pacman >/dev/null 2>&1; then
        print_info "Detected Arch Linux system"
        pacman -S --noconfirm \
            libbpf \
            linux-headers \
            clang \
            llvm \
            gcc \
            make
    else
        print_error "Unknown package manager. Please install dependencies manually:"
        echo "  - libbpf development headers"
        echo "  - kernel headers"
        echo "  - clang/llvm"
        echo "  - build tools (gcc, make)"
        exit 1
    fi
    
    print_success "Dependencies installed successfully"
}

# Verify dependencies
verify_dependencies() {
    print_info "Verifying dependencies..."
    
    # Check Go
    if ! command -v go >/dev/null 2>&1; then
        print_error "Go is not installed"
        exit 1
    fi
    print_success "Go found: $(go version)"
    
    # Check Clang
    if ! command -v clang >/dev/null 2>&1; then
        print_error "Clang is not installed"
        exit 1
    fi
    print_success "Clang found: $(clang --version | head -n1)"
    
    # Check eBPF headers
    if [ -f /usr/include/bpf/bpf_helpers.h ]; then
        print_success "eBPF headers found at /usr/include/bpf/bpf_helpers.h"
    elif [ -f /usr/local/include/bpf/bpf_helpers.h ]; then
        print_success "eBPF headers found at /usr/local/include/bpf/bpf_helpers.h"
    else
        print_error "eBPF headers not found"
        exit 1
    fi
    
    # Check kernel headers
    KERNEL_VERSION=$(uname -r)
    if [ -d "/lib/modules/$KERNEL_VERSION/build" ]; then
        print_success "Kernel headers found for $KERNEL_VERSION"
    else
        print_warning "Kernel headers may not be properly installed"
    fi
}

# Clean previous build artifacts
clean_build() {
    print_info "Cleaning previous build artifacts..."
    
    # Remove generated eBPF files
    rm -f internal/pkg/ebpf/*_bpfel.go
    rm -f internal/pkg/ebpf/*_bpfeb.go
    rm -f internal/pkg/ebpf/*.o
    
    # Clean build directory
    rm -rf build/bin/*
    rm -rf build/tmp/*
    
    print_success "Build artifacts cleaned"
}

# Build the project
build_project() {
    print_info "Building TalusAegis for Linux..."
    
    # Install Go dependencies
    print_info "Installing Go dependencies..."
    go mod download
    go mod tidy
    
    # Install bpf2go tool
    print_info "Installing eBPF code generation tools..."
    go install github.com/cilium/ebpf/cmd/bpf2go@latest
    
    # Generate eBPF bindings
    print_info "Generating eBPF Go bindings..."
    cd internal/pkg/ebpf
    go generate ./...
    cd ../../..
    
    # Verify eBPF files were generated
    if [ ! -f "internal/pkg/ebpf/icmp_responder_bpfel.go" ]; then
        print_error "eBPF Go bindings were not generated"
        exit 1
    fi
    print_success "eBPF bindings generated successfully"
    
    # Build the binary
    print_info "Building binary..."
    
    # Get build information
    VERSION=$(git describe --tags --always --dirty 2>/dev/null || echo "unknown")
    COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
    
    # Build for Linux
    CGO_ENABLED=1 GOOS=linux go build \
        -ldflags "-X main.version=$VERSION -X main.commit=$COMMIT -X main.date=$DATE -s -w" \
        -o build/bin/talusaegis-linux \
        ./cmd/talusaegis
    
    print_success "Build completed successfully"
    print_info "Binary location: build/bin/talusaegis-linux"
}

# Run tests
run_tests() {
    print_info "Running tests..."
    
    # Run unit tests
    go test -v -race ./...
    
    print_success "All tests passed"
}

# Main function
main() {
    print_info "TalusAegis Remote Build Script"
    print_info "=============================="
    echo
    
    # Parse command line arguments
    INSTALL_DEPS=false
    SKIP_TESTS=false
    CLEAN_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --install-deps)
                INSTALL_DEPS=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --clean-only)
                CLEAN_ONLY=true
                shift
                ;;
            -h|--help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --install-deps    Install system dependencies"
                echo "  --skip-tests      Skip running tests"
                echo "  --clean-only      Only clean build artifacts"
                echo "  -h, --help        Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Check platform
    check_platform
    
    # Install dependencies if requested
    if [ "$INSTALL_DEPS" = true ]; then
        install_dependencies
    fi
    
    # Verify dependencies
    verify_dependencies
    
    # Clean build artifacts
    clean_build
    
    # Exit if clean-only
    if [ "$CLEAN_ONLY" = true ]; then
        print_info "Clean completed"
        exit 0
    fi
    
    # Build project
    build_project
    
    # Run tests unless skipped
    if [ "$SKIP_TESTS" = false ]; then
        run_tests
    fi
    
    echo
    print_success "Remote build completed successfully!"
    print_info "You can now run: ./build/bin/talusaegis-linux --help"
}

# Run main function with all arguments
main "$@"
