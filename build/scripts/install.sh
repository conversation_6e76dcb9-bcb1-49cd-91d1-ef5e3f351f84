#!/bin/bash

# TalusAegis Installation Script
# This script installs TalusAegis and its dependencies

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
INSTALL_DIR="/usr/local/bin"
CONFIG_DIR="/etc/talusaegis"
LOG_DIR="/var/log/talusaegis"
SERVICE_DIR="/etc/systemd/system"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_root() {
    if [[ $EUID -ne 0 ]]; then
        log_error "This script must be run as root"
        exit 1
    fi
}

check_system() {
    log_info "Checking system requirements..."
    
    # Check Linux
    if [[ "$(uname -s)" != "Linux" ]]; then
        log_error "This software only supports Linux"
        exit 1
    fi
    
    # Check kernel version
    KERNEL_VERSION=$(uname -r | cut -d. -f1-2)
    REQUIRED_VERSION="4.18"
    
    if ! awk "BEGIN {exit !($KERNEL_VERSION >= $REQUIRED_VERSION)}"; then
        log_error "Kernel version $KERNEL_VERSION is too old. Required: $REQUIRED_VERSION+"
        exit 1
    fi
    
    log_info "System requirements met"
}

install_dependencies() {
    log_info "Installing system dependencies..."
    
    if command -v apt-get >/dev/null 2>&1; then
        # Debian/Ubuntu
        apt-get update
        apt-get install -y build-essential clang llvm libbpf-dev linux-headers-$(uname -r) curl
    elif command -v yum >/dev/null 2>&1; then
        # CentOS/RHEL
        yum install -y gcc clang llvm-devel kernel-devel-$(uname -r) curl
    elif command -v dnf >/dev/null 2>&1; then
        # Fedora
        dnf install -y gcc clang llvm-devel kernel-devel curl
    else
        log_warn "Unknown package manager. Please install dependencies manually:"
        log_warn "- build-essential/gcc"
        log_warn "- clang"
        log_warn "- llvm"
        log_warn "- libbpf-dev/llvm-devel"
        log_warn "- linux-headers/kernel-devel"
    fi
}

create_directories() {
    log_info "Creating directories..."
    
    mkdir -p "$CONFIG_DIR"
    mkdir -p "$LOG_DIR"
    
    # Set permissions
    chmod 755 "$CONFIG_DIR"
    chmod 755 "$LOG_DIR"
}

install_binary() {
    log_info "Installing TalusAegis binary..."
    
    # Detect architecture
    ARCH=$(uname -m)
    case $ARCH in
        x86_64)
            BINARY_NAME="talusaegis-linux-amd64"
            ;;
        aarch64|arm64)
            BINARY_NAME="talusaegis-linux-arm64"
            ;;
        *)
            log_error "Unsupported architecture: $ARCH"
            exit 1
            ;;
    esac
    
    # Check if binary exists
    if [[ ! -f "build/bin/$BINARY_NAME" ]]; then
        log_error "Binary not found: build/bin/$BINARY_NAME"
        log_error "Please run 'make build' first"
        exit 1
    fi
    
    # Install binary
    cp "build/bin/$BINARY_NAME" "$INSTALL_DIR/talusaegis"
    chmod +x "$INSTALL_DIR/talusaegis"
    
    log_info "Binary installed to $INSTALL_DIR/talusaegis"
}

install_config() {
    log_info "Installing configuration files..."
    
    # Install default config if it doesn't exist
    if [[ ! -f "$CONFIG_DIR/config.yaml" ]]; then
        if [[ -f "configs/config.yaml" ]]; then
            cp "configs/config.yaml" "$CONFIG_DIR/config.yaml"
            log_info "Default configuration installed to $CONFIG_DIR/config.yaml"
        else
            log_warn "Default configuration not found, creating minimal config"
            cat > "$CONFIG_DIR/config.yaml" << EOF
server:
  host: "0.0.0.0"
  port: 8080

icmp:
  interface: "eth0"
  enabled: true
  ttl: 64

log:
  level: "info"
  format: "json"
  output: "$LOG_DIR/talusaegis.log"
EOF
        fi
    else
        log_info "Configuration file already exists, skipping"
    fi
}

install_systemd_service() {
    log_info "Installing systemd service..."
    
    cat > "$SERVICE_DIR/talusaegis.service" << EOF
[Unit]
Description=TalusAegis eBPF ICMP Responder
Documentation=https://github.com/your-org/talusaegis
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=$INSTALL_DIR/talusaegis run --config $CONFIG_DIR/config.yaml
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=talusaegis

# Security settings
NoNewPrivileges=false
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$LOG_DIR $CONFIG_DIR

[Install]
WantedBy=multi-user.target
EOF
    
    # Reload systemd
    systemctl daemon-reload
    
    log_info "Systemd service installed"
    log_info "Enable with: systemctl enable talusaegis"
    log_info "Start with: systemctl start talusaegis"
}

verify_installation() {
    log_info "Verifying installation..."
    
    # Check binary
    if [[ ! -x "$INSTALL_DIR/talusaegis" ]]; then
        log_error "Binary not found or not executable"
        exit 1
    fi
    
    # Check version
    VERSION=$("$INSTALL_DIR/talusaegis" --version 2>/dev/null || echo "unknown")
    log_info "Installed version: $VERSION"
    
    # Check config
    if [[ ! -f "$CONFIG_DIR/config.yaml" ]]; then
        log_error "Configuration file not found"
        exit 1
    fi
    
    # Validate config
    if ! "$INSTALL_DIR/talusaegis" config validate --config "$CONFIG_DIR/config.yaml" >/dev/null 2>&1; then
        log_warn "Configuration validation failed, please check $CONFIG_DIR/config.yaml"
    fi
    
    log_info "Installation verified successfully"
}

main() {
    log_info "Starting TalusAegis installation..."
    
    check_root
    check_system
    install_dependencies
    create_directories
    install_binary
    install_config
    install_systemd_service
    verify_installation
    
    log_info "Installation completed successfully!"
    log_info ""
    log_info "Next steps:"
    log_info "1. Review configuration: $CONFIG_DIR/config.yaml"
    log_info "2. Enable service: systemctl enable talusaegis"
    log_info "3. Start service: systemctl start talusaegis"
    log_info "4. Check status: systemctl status talusaegis"
    log_info "5. View logs: journalctl -u talusaegis -f"
}

# Run main function
main "$@"
