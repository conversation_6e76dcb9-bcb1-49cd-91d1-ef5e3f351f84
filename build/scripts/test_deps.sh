#!/bin/bash

# Test script to simulate different Linux environments for dependency checking

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test dependency detection for different Linux distributions
test_debian_detection() {
    print_info "Testing Debian/Ubuntu detection..."
    
    # Simulate Debian environment
    if command -v apt-get >/dev/null 2>&1; then
        print_success "apt-get found - Debian/Ubuntu system detected"
        print_info "Required packages for Debian/Ubuntu:"
        echo "  - libbpf-dev"
        echo "  - linux-headers-\$(uname -r)"
        echo "  - clang"
        echo "  - llvm"
        echo "  - build-essential"
    else
        print_warning "apt-get not found - not a Debian/Ubuntu system"
    fi
}

test_redhat_detection() {
    print_info "Testing RHEL/CentOS detection..."
    
    # Simulate RHEL environment
    if command -v yum >/dev/null 2>&1; then
        print_success "yum found - RHEL/CentOS system detected"
        print_info "Required packages for RHEL/CentOS:"
        echo "  - libbpf-devel"
        echo "  - kernel-devel"
        echo "  - clang"
        echo "  - llvm"
        echo "  - gcc"
    else
        print_warning "yum not found - not a RHEL/CentOS system"
    fi
}

test_fedora_detection() {
    print_info "Testing Fedora detection..."
    
    # Simulate Fedora environment
    if command -v dnf >/dev/null 2>&1; then
        print_success "dnf found - Fedora system detected"
        print_info "Required packages for Fedora:"
        echo "  - libbpf-devel"
        echo "  - kernel-devel"
        echo "  - clang"
        echo "  - llvm"
        echo "  - gcc"
    else
        print_warning "dnf not found - not a Fedora system"
    fi
}

test_arch_detection() {
    print_info "Testing Arch Linux detection..."
    
    # Simulate Arch environment
    if command -v pacman >/dev/null 2>&1; then
        print_success "pacman found - Arch Linux system detected"
        print_info "Required packages for Arch Linux:"
        echo "  - libbpf"
        echo "  - linux-headers"
        echo "  - clang"
        echo "  - llvm"
        echo "  - gcc"
    else
        print_warning "pacman not found - not an Arch Linux system"
    fi
}

test_macos_detection() {
    print_info "Testing macOS detection..."
    
    if [[ "$OSTYPE" == "darwin"* ]]; then
        print_success "macOS system detected"
        if command -v brew >/dev/null 2>&1; then
            print_success "Homebrew found"
            print_info "Required packages for macOS:"
            echo "  - llvm (via Homebrew)"
        else
            print_warning "Homebrew not found"
            print_info "Please install Homebrew first: https://brew.sh"
        fi
    else
        print_warning "Not a macOS system"
    fi
}

# Test eBPF header detection
test_ebpf_headers() {
    print_info "Testing eBPF header detection..."
    
    if [ -f /usr/include/bpf/bpf_helpers.h ]; then
        print_success "eBPF headers found at /usr/include/bpf/bpf_helpers.h"
    elif [ -f /usr/local/include/bpf/bpf_helpers.h ]; then
        print_success "eBPF headers found at /usr/local/include/bpf/bpf_helpers.h"
    else
        print_warning "eBPF headers not found"
        print_info "This is expected on non-Linux systems or systems without libbpf-dev installed"
    fi
}

# Test Go installation
test_go_installation() {
    print_info "Testing Go installation..."
    
    if command -v go >/dev/null 2>&1; then
        GO_VERSION=$(go version | cut -d' ' -f3)
        print_success "Go found: $GO_VERSION"
        
        # Check Go version (should be 1.21+)
        GO_MAJOR=$(echo $GO_VERSION | sed 's/go//' | cut -d'.' -f1)
        GO_MINOR=$(echo $GO_VERSION | sed 's/go//' | cut -d'.' -f2)
        
        if [ "$GO_MAJOR" -gt 1 ] || ([ "$GO_MAJOR" -eq 1 ] && [ "$GO_MINOR" -ge 21 ]); then
            print_success "Go version is compatible (1.21+)"
        else
            print_warning "Go version may be too old. Recommended: 1.21+"
        fi
    else
        print_error "Go not found. Please install Go 1.21 or later"
    fi
}

# Test Clang installation
test_clang_installation() {
    print_info "Testing Clang installation..."
    
    if command -v clang >/dev/null 2>&1; then
        CLANG_VERSION=$(clang --version | head -n1)
        print_success "Clang found: $CLANG_VERSION"
    else
        if [[ "$OSTYPE" == "darwin"* ]]; then
            print_warning "Clang not found (this is OK on macOS for development)"
        else
            print_error "Clang not found. Required for eBPF compilation on Linux"
        fi
    fi
}

# Main test function
main() {
    print_info "TalusAegis Dependency Test Script"
    print_info "================================="
    echo
    
    print_info "Current system: $(uname -s) $(uname -r)"
    echo
    
    test_go_installation
    echo
    
    test_clang_installation
    echo
    
    test_ebpf_headers
    echo
    
    print_info "Testing package manager detection..."
    test_debian_detection
    test_redhat_detection
    test_fedora_detection
    test_arch_detection
    test_macos_detection
    
    echo
    print_info "Test completed!"
    print_info "Use 'make install-system-deps' to see installation commands for your system"
    print_info "Use 'make install-deps-auto' to automatically install dependencies (requires sudo on Linux)"
}

# Run main function
main "$@"
