package main

import (
	"fmt"
	"os"
)

const mockEBPFCode = `// Code generated by bpf2go; DO NOT EDIT.
//go:build !linux

package ebpf

import (
	"bytes"
	_ "embed"
	"fmt"
	"io"

	"github.com/cilium/ebpf"
)

// icmp_responderObjects contains all objects after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderObjects or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderObjects struct {
	icmp_responderPrograms
	icmp_responderMaps
}

func (o *icmp_responderObjects) Close() error {
	return _Icmp_responderClose(
		&o.icmp_responderPrograms,
		&o.icmp_responderMaps,
	)
}

// icmp_responderMaps contains all maps after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderMaps or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderMaps struct {
	ConfigMap *ebpf.Map ` + "`" + `ebpf:"config_map"` + "`" + `
	StatsMap  *ebpf.Map ` + "`" + `ebpf:"stats_map"` + "`" + `
}

func (m *icmp_responderMaps) Close() error {
	return _Icmp_responderClose(
		m.ConfigMap,
		m.StatsMap,
	)
}

// icmp_responderPrograms contains all programs after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderPrograms or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderPrograms struct {
	IcmpResponderMain *ebpf.Program ` + "`" + `ebpf:"icmp_responder_main"` + "`" + `
}

func (p *icmp_responderPrograms) Close() error {
	return _Icmp_responderClose(
		p.IcmpResponderMain,
	)
}

func _Icmp_responderClose(closers ...io.Closer) error {
	for _, closer := range closers {
		if err := closer.Close(); err != nil {
			return err
		}
	}
	return nil
}

// Do not access this directly.
//
//go:embed icmp_responder_bpfel.o
var _Icmp_responderBytes []byte

// loadIcmp_responder returns the embedded CollectionSpec for icmp_responder.
func loadIcmp_responder() (*ebpf.CollectionSpec, error) {
	reader := bytes.NewReader(_Icmp_responderBytes)
	spec, err := ebpf.LoadCollectionSpecFromReader(reader)
	if err != nil {
		return nil, fmt.Errorf("can't load icmp_responder: %w", err)
	}

	return spec, err
}

// loadIcmp_responderObjects loads icmp_responder and converts it into a struct.
//
// The following types are suitable as obj argument:
//
//	*icmp_responderObjects
//	*icmp_responderPrograms
//	*icmp_responderMaps
//
// See ebpf.CollectionSpec.LoadAndAssign documentation for details.
func loadIcmp_responderObjects(obj interface{}, opts *ebpf.CollectionOptions) error {
	// Mock implementation for non-Linux platforms
	fmt.Println("Warning: Using mock eBPF implementation on non-Linux platform")
	return fmt.Errorf("eBPF not supported on this platform")
}
`

func main() {
	// Write the mock eBPF generated code
	if err := os.WriteFile("icmp_responder_bpfel.go", []byte(mockEBPFCode), 0644); err != nil {
		fmt.Printf("Error writing mock eBPF file: %v\n", err)
		os.Exit(1)
	}

	// Create empty object file
	if err := os.WriteFile("icmp_responder_bpfel.o", []byte{}, 0644); err != nil {
		fmt.Printf("Error writing mock object file: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Mock eBPF objects generated successfully")
}
