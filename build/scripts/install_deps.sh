#!/bin/bash

# TalusAegis Dependency Installation Script
# This script automatically installs system dependencies based on the detected platform

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "This script requires root privileges to install system packages"
        print_info "Please run with sudo: sudo $0"
        exit 1
    fi
}

# Detect the operating system
detect_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        if [ -f /etc/debian_version ]; then
            OS="debian"
        elif [ -f /etc/redhat-release ]; then
            OS="redhat"
        elif [ -f /etc/fedora-release ]; then
            OS="fedora"
        elif [ -f /etc/arch-release ]; then
            OS="arch"
        else
            OS="linux"
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
    
    print_info "Detected OS: $OS"
}

# Install dependencies for Debian/Ubuntu
install_debian_deps() {
    print_info "Installing dependencies for Debian/Ubuntu..."
    
    apt update
    apt install -y \
        libbpf-dev \
        linux-headers-$(uname -r) \
        clang \
        llvm \
        build-essential \
        pkg-config \
        git \
        curl \
        wget
    
    print_success "Debian/Ubuntu dependencies installed successfully"
}

# Install dependencies for RHEL/CentOS
install_redhat_deps() {
    print_info "Installing dependencies for RHEL/CentOS..."
    
    yum install -y \
        libbpf-devel \
        kernel-devel \
        clang \
        llvm \
        gcc \
        gcc-c++ \
        make \
        pkgconfig \
        git \
        curl \
        wget
    
    print_success "RHEL/CentOS dependencies installed successfully"
}

# Install dependencies for Fedora
install_fedora_deps() {
    print_info "Installing dependencies for Fedora..."
    
    dnf install -y \
        libbpf-devel \
        kernel-devel \
        clang \
        llvm \
        gcc \
        gcc-c++ \
        make \
        pkgconf-pkg-config \
        git \
        curl \
        wget
    
    print_success "Fedora dependencies installed successfully"
}

# Install dependencies for Arch Linux
install_arch_deps() {
    print_info "Installing dependencies for Arch Linux..."
    
    pacman -S --noconfirm \
        libbpf \
        linux-headers \
        clang \
        llvm \
        gcc \
        make \
        pkgconfig \
        git \
        curl \
        wget
    
    print_success "Arch Linux dependencies installed successfully"
}

# Install dependencies for macOS
install_macos_deps() {
    print_info "Installing dependencies for macOS..."
    
    # Check if Homebrew is installed
    if ! command -v brew &> /dev/null; then
        print_warning "Homebrew not found. Installing Homebrew..."
        /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    fi
    
    # Install dependencies via Homebrew
    brew install llvm git curl wget
    
    print_success "macOS dependencies installed successfully"
    print_info "Note: eBPF functionality is not available on macOS. Mock implementation will be used for development."
}

# Verify installation
verify_installation() {
    print_info "Verifying installation..."
    
    # Check Go
    if ! command -v go &> /dev/null; then
        print_warning "Go is not installed. Please install Go 1.21 or later from https://golang.org/dl/"
    else
        print_success "Go found: $(go version)"
    fi
    
    # Check Clang (only on Linux)
    if [ "$OS" != "macos" ]; then
        if ! command -v clang &> /dev/null; then
            print_error "Clang not found"
            exit 1
        else
            print_success "Clang found: $(clang --version | head -n1)"
        fi
        
        # Check eBPF headers
        if [ -f /usr/include/bpf/bpf_helpers.h ] || [ -f /usr/local/include/bpf/bpf_helpers.h ]; then
            print_success "eBPF headers found"
        else
            print_error "eBPF headers not found"
            exit 1
        fi
    fi
    
    print_success "All dependencies verified successfully"
}

# Main function
main() {
    print_info "TalusAegis Dependency Installation Script"
    print_info "========================================"
    
    detect_os
    
    if [ "$OS" != "macos" ]; then
        check_root
    fi
    
    case $OS in
        "debian")
            install_debian_deps
            ;;
        "redhat")
            install_redhat_deps
            ;;
        "fedora")
            install_fedora_deps
            ;;
        "arch")
            install_arch_deps
            ;;
        "macos")
            install_macos_deps
            ;;
        *)
            print_error "Unsupported OS: $OS"
            exit 1
            ;;
    esac
    
    verify_installation
    
    print_success "Dependency installation completed successfully!"
    print_info "You can now run 'make build' to build TalusAegis"
}

# Run main function
main "$@"
