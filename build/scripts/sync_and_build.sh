#!/bin/bash

# Sync and build script for TalusAegis
# This script syncs code to remote server and builds it there

set -e

# Configuration
REMOTE_HOST="root@*************"
REMOTE_DIR="/root/talusaegis"
LOCAL_DIR="$(pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the project root
check_project_root() {
    if [ ! -f "go.mod" ] || [ ! -d "cmd/talusaegis" ]; then
        print_error "This script must be run from the project root directory"
        exit 1
    fi
}

# Sync code to remote server
sync_code() {
    print_info "Syncing code to remote server..."
    
    # Create exclude file for rsync
    cat > /tmp/rsync_exclude << EOF
.git/
.DS_Store
*.log
build/bin/
build/tmp/
build/dist/
coverage.out
*.test
.vscode/
.idea/
node_modules/
EOF
    
    # Sync code using rsync
    rsync -avz --delete \
        --exclude-from=/tmp/rsync_exclude \
        "$LOCAL_DIR/" "$REMOTE_HOST:$REMOTE_DIR/"
    
    # Clean up
    rm -f /tmp/rsync_exclude
    
    print_success "Code synced successfully"
}

# Execute remote build
remote_build() {
    local build_args="$1"
    
    print_info "Executing remote build..."
    
    # Execute build script on remote server
    ssh "$REMOTE_HOST" "cd $REMOTE_DIR && bash build/scripts/remote_build.sh $build_args"
    
    print_success "Remote build completed"
}

# Copy binary back to local
copy_binary() {
    print_info "Copying binary back to local machine..."
    
    # Create local bin directory if it doesn't exist
    mkdir -p build/bin/
    
    # Copy the binary
    scp "$REMOTE_HOST:$REMOTE_DIR/build/bin/talusaegis-linux" build/bin/
    
    print_success "Binary copied to build/bin/talusaegis-linux"
}

# Show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "This script syncs code to remote server and builds TalusAegis there."
    echo ""
    echo "Options:"
    echo "  --install-deps    Install system dependencies on remote server"
    echo "  --skip-tests      Skip running tests on remote server"
    echo "  --clean-only      Only clean build artifacts on remote server"
    echo "  --no-copy         Don't copy binary back to local machine"
    echo "  --sync-only       Only sync code, don't build"
    echo "  -h, --help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                          # Sync and build"
    echo "  $0 --install-deps           # Install deps, sync and build"
    echo "  $0 --skip-tests --no-copy   # Sync, build without tests, don't copy back"
    echo "  $0 --sync-only              # Only sync code"
}

# Test remote connection
test_connection() {
    print_info "Testing connection to remote server..."
    
    if ssh -o ConnectTimeout=10 "$REMOTE_HOST" "echo 'Connection successful'" >/dev/null 2>&1; then
        print_success "Connection to $REMOTE_HOST successful"
    else
        print_error "Cannot connect to $REMOTE_HOST"
        print_info "Please ensure:"
        print_info "  1. SSH key is properly configured"
        print_info "  2. Remote server is accessible"
        print_info "  3. Remote host address is correct"
        exit 1
    fi
}

# Main function
main() {
    print_info "TalusAegis Sync and Build Script"
    print_info "================================"
    echo
    
    # Parse command line arguments
    BUILD_ARGS=""
    NO_COPY=false
    SYNC_ONLY=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --install-deps)
                BUILD_ARGS="$BUILD_ARGS --install-deps"
                shift
                ;;
            --skip-tests)
                BUILD_ARGS="$BUILD_ARGS --skip-tests"
                shift
                ;;
            --clean-only)
                BUILD_ARGS="$BUILD_ARGS --clean-only"
                shift
                ;;
            --no-copy)
                NO_COPY=true
                shift
                ;;
            --sync-only)
                SYNC_ONLY=true
                shift
                ;;
            -h|--help)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check project root
    check_project_root
    
    # Test connection
    test_connection
    
    # Sync code
    sync_code
    
    # Exit if sync-only
    if [ "$SYNC_ONLY" = true ]; then
        print_info "Sync completed"
        exit 0
    fi
    
    # Execute remote build
    remote_build "$BUILD_ARGS"
    
    # Copy binary back unless --no-copy is specified
    if [ "$NO_COPY" = false ]; then
        copy_binary
    fi
    
    echo
    print_success "Sync and build completed successfully!"
    
    if [ "$NO_COPY" = false ]; then
        print_info "Local binary: build/bin/talusaegis-linux"
        print_info "You can test it with: file build/bin/talusaegis-linux"
    fi
    
    print_info "Remote binary: $REMOTE_HOST:$REMOTE_DIR/build/bin/talusaegis-linux"
}

# Run main function with all arguments
main "$@"
