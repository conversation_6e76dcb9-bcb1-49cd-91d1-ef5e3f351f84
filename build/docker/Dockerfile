# Multi-stage build for TalusAegis
# Stage 1: Build environment
FROM ubuntu:22.04 AS builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    clang \
    llvm \
    libbpf-dev \
    linux-headers-generic \
    pkg-config \
    curl \
    git \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Install Go
ARG GO_VERSION=1.23.0
RUN curl -fsSL https://golang.org/dl/go${GO_VERSION}.linux-amd64.tar.gz | tar -xzC /usr/local
ENV PATH="/usr/local/go/bin:${PATH}"

# Set working directory
WORKDIR /src

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN make build-amd64

# Stage 2: Runtime environment
FROM ubuntu:22.04

# Install runtime dependencies
RUN apt-get update && apt-get install -y \
    ca-certificates \
    iproute2 \
    iputils-ping \
    net-tools \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN groupadd -r talusaegis && useradd -r -g talusaegis talusaegis

# Create directories
RUN mkdir -p /etc/talusaegis /var/log/talusaegis /var/lib/talusaegis

# Copy binary from builder stage
COPY --from=builder /src/build/bin/talusaegis-linux-amd64 /usr/local/bin/talusaegis

# Copy configuration files
COPY configs/ /etc/talusaegis/

# Set permissions
RUN chmod +x /usr/local/bin/talusaegis && \
    chown -R talusaegis:talusaegis /etc/talusaegis /var/log/talusaegis /var/lib/talusaegis

# Expose HTTP port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Set user
USER talusaegis

# Default command
CMD ["/usr/local/bin/talusaegis", "run"]
