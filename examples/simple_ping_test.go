package main

import (
	"fmt"
	"net"
	"os"
	"time"

	"golang.org/x/net/icmp"
	"golang.org/x/net/ipv4"
)

// SimplePingTest demonstrates how to test TalusAegis ICMP responder
func main() {
	if len(os.Args) < 2 {
		fmt.Println("Usage: go run simple_ping_test.go <target_ip>")
		fmt.Println("Example: go run simple_ping_test.go *************")
		os.Exit(1)
	}

	target := os.Args[1]

	// Validate IP address
	dst, err := net.ResolveIPAddr("ip4", target)
	if err != nil {
		fmt.Printf("Error resolving IP address: %v\n", err)
		os.Exit(1)
	}

	fmt.Printf("PING %s (%s)\n", target, dst.IP)

	// Create ICMP connection
	conn, err := icmp.ListenPacket("ip4:icmp", "0.0.0.0")
	if err != nil {
		fmt.Printf("Error creating ICMP connection: %v\n", err)
		os.Exit(1)
	}
	defer conn.Close()

	// Send 5 ping packets
	for i := 0; i < 5; i++ {
		if err := sendPing(conn, dst, i+1); err != nil {
			fmt.Printf("Error sending ping %d: %v\n", i+1, err)
			continue
		}

		// Wait a bit between pings
		time.Sleep(1 * time.Second)
	}

	fmt.Println("\nPing test completed")
}

func sendPing(conn *icmp.PacketConn, dst *net.IPAddr, seq int) error {
	// Create ICMP message
	message := &icmp.Message{
		Type: ipv4.ICMPTypeEcho,
		Code: 0,
		Body: &icmp.Echo{
			ID:   os.Getpid() & 0xffff,
			Seq:  seq,
			Data: []byte(fmt.Sprintf("TalusAegis test packet %d", seq)),
		},
	}

	// Marshal the message
	data, err := message.Marshal(nil)
	if err != nil {
		return fmt.Errorf("failed to marshal ICMP message: %w", err)
	}

	// Record send time
	start := time.Now()

	// Send the packet
	_, err = conn.WriteTo(data, dst)
	if err != nil {
		return fmt.Errorf("failed to send packet: %w", err)
	}

	// Set read timeout
	conn.SetReadDeadline(time.Now().Add(3 * time.Second))

	// Read response
	reply := make([]byte, 1500)
	n, peer, err := conn.ReadFrom(reply)
	if err != nil {
		return fmt.Errorf("failed to read reply: %w", err)
	}

	// Calculate round-trip time
	duration := time.Since(start)

	// Parse the reply
	replyMsg, err := icmp.ParseMessage(int(ipv4.ICMPTypeEchoReply), reply[:n])
	if err != nil {
		return fmt.Errorf("failed to parse reply: %w", err)
	}

	// Verify it's an echo reply
	if replyMsg.Type != ipv4.ICMPTypeEchoReply {
		return fmt.Errorf("received unexpected ICMP type: %v", replyMsg.Type)
	}

	// Extract echo reply data
	echoReply, ok := replyMsg.Body.(*icmp.Echo)
	if !ok {
		return fmt.Errorf("invalid echo reply body")
	}

	// Verify sequence number
	if echoReply.Seq != seq {
		return fmt.Errorf("sequence mismatch: expected %d, got %d", seq, echoReply.Seq)
	}

	fmt.Printf("64 bytes from %s: icmp_seq=%d time=%.3fms\n",
		peer, echoReply.Seq, float64(duration.Nanoseconds())/1000000.0)

	return nil
}
