# TalusAegis Configuration File
# eBPF-based ICMP responder configuration

# Server configuration
server:
  host: "0.0.0.0"          # Server bind address
  port: 8080               # HTTP API port
  read_timeout: 30         # Read timeout in seconds
  write_timeout: 30        # Write timeout in seconds

# ICMP responder configuration
icmp:
  interface: "eth0"        # Network interface to attach to
  enabled: true            # Enable/disable ICMP responder
  ttl: 64                  # TTL value for ICMP replies
  max_size: 1500           # Maximum packet size to process

# Logging configuration
log:
  level: "info"            # Log level: debug, info, warn, error, fatal
  format: "json"           # Log format: json, console
  output: "stdout"         # Log output: stdout, stderr, or file path
  max_size: 100            # Maximum log file size in MB (for file output)
  max_backups: 3           # Maximum number of log file backups
  max_age: 28              # Maximum age of log files in days
  compress: true           # Compress rotated log files
