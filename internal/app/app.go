package app

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"sync"
	"time"

	"go.uber.org/zap"

	"talusaegis/internal/pkg/config"
	"talusaegis/internal/pkg/ebpf"
	"talusaegis/internal/pkg/logger"
	"talusaegis/pkg/types"
)

// Application represents the main application
type Application struct {
	logger    *zap.Logger
	config    *config.Config
	responder *ebpf.ICMPResponder
	server    *http.Server
	wg        sync.WaitGroup
	mu        sync.RWMutex
	running   bool
}

// New creates a new application instance
func New(log *zap.Logger, cfg *config.Config) (*Application, error) {
	if log == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	app := &Application{
		logger: logger.WithComponent(log, "app"),
		config: cfg,
	}

	return app, nil
}

// Run starts the application
func (a *Application) Run(ctx context.Context) error {
	a.mu.Lock()
	if a.running {
		a.mu.Unlock()
		return fmt.Errorf("application is already running")
	}
	a.running = true
	a.mu.Unlock()

	defer func() {
		a.mu.Lock()
		a.running = false
		a.mu.Unlock()
	}()

	a.logger.Info("Starting TalusAegis application")

	// Initialize ICMP responder
	if err := a.initICMPResponder(); err != nil {
		return fmt.Errorf("failed to initialize ICMP responder: %w", err)
	}

	// Start HTTP server
	if err := a.startHTTPServer(); err != nil {
		return fmt.Errorf("failed to start HTTP server: %w", err)
	}

	// Start monitoring
	a.startMonitoring(ctx)

	// Wait for context cancellation
	<-ctx.Done()

	a.logger.Info("Shutting down application")

	// Shutdown gracefully
	return a.shutdown()
}

// initICMPResponder initializes the eBPF ICMP responder
func (a *Application) initICMPResponder() error {
	a.logger.Info("Initializing ICMP responder", zap.String("interface", a.config.ICMP.Interface))

	// Create ICMP responder configuration
	ebpfConfig := &ebpf.Config{
		Interface: a.config.ICMP.Interface,
		Enabled:   a.config.ICMP.Enabled,
		TTL:       a.config.ICMP.TTL,
		MaxSize:   a.config.ICMP.MaxSize,
		Debug:     a.config.ICMP.Debug,
	}

	// Create ICMP responder
	responder, err := ebpf.NewICMPResponder(a.logger, ebpfConfig)
	if err != nil {
		return fmt.Errorf("failed to create ICMP responder: %w", err)
	}

	// Load eBPF program
	if err := responder.Load(); err != nil {
		return fmt.Errorf("failed to load eBPF program: %w", err)
	}

	a.responder = responder
	a.logger.Info("ICMP responder initialized successfully")

	return nil
}

// startHTTPServer starts the HTTP server for status API
func (a *Application) startHTTPServer() error {
	mux := http.NewServeMux()

	// Health check endpoint
	mux.HandleFunc("/health", a.handleHealth)

	// Status endpoint
	mux.HandleFunc("/status", a.handleStatus)

	// Statistics endpoint
	mux.HandleFunc("/stats", a.handleStats)

	// Debug logs endpoint
	mux.HandleFunc("/debug", a.handleDebugLogs)

	// Configuration endpoint
	mux.HandleFunc("/config", a.handleConfig)

	// Control endpoints
	mux.HandleFunc("/enable", a.handleEnable)
	mux.HandleFunc("/disable", a.handleDisable)

	addr := fmt.Sprintf("%s:%d", a.config.Server.Host, a.config.Server.Port)
	a.server = &http.Server{
		Addr:         addr,
		Handler:      mux,
		ReadTimeout:  time.Duration(a.config.Server.ReadTimeout) * time.Second,
		WriteTimeout: time.Duration(a.config.Server.WriteTimeout) * time.Second,
	}

	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		a.logger.Info("Starting HTTP server", zap.String("address", addr))
		if err := a.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			a.logger.Error("HTTP server error", zap.Error(err))
		}
	}()

	return nil
}

// startMonitoring starts the monitoring goroutine
func (a *Application) startMonitoring(ctx context.Context) {
	a.wg.Add(1)
	go func() {
		defer a.wg.Done()
		ticker := time.NewTicker(30 * time.Second)
		defer ticker.Stop()

		for {
			select {
			case <-ctx.Done():
				return
			case <-ticker.C:
				a.logStats()
			}
		}
	}()
}

// logStats logs current statistics
func (a *Application) logStats() {
	if a.responder == nil {
		return
	}

	stats, err := a.responder.GetStats()
	if err != nil {
		a.logger.Error("Failed to get statistics", zap.Error(err))
		return
	}

	a.logger.Info("Packet statistics",
		zap.Uint64("received", stats.Received),
		zap.Uint64("processed", stats.Processed),
		zap.Uint64("responded", stats.Responded),
		zap.Uint64("dropped", stats.Dropped),
		zap.Uint64("errors", stats.Errors))

	// Log debug information if debug mode is enabled
	if debugLogs, err := a.responder.GetDebugLogs(); err == nil && len(debugLogs) > 0 {
		a.logger.Debug("Recent packet processing debug logs",
			zap.Int("count", len(debugLogs)))

		// Log the most recent 5 entries
		maxLogs := 5
		if len(debugLogs) < maxLogs {
			maxLogs = len(debugLogs)
		}

		for i := len(debugLogs) - maxLogs; i < len(debugLogs); i++ {
			log := debugLogs[i]
			a.logger.Debug("Packet debug",
				zap.Uint32("timestamp", log.Timestamp),
				zap.String("src_ip", log.SrcIP),
				zap.String("dst_ip", log.DstIP),
				zap.Uint16("icmp_id", log.ICMPID),
				zap.Uint16("icmp_seq", log.ICMPSeq),
				zap.String("action", log.Action))
		}
	}
}

// shutdown gracefully shuts down the application
func (a *Application) shutdown() error {
	var errs []error

	// Shutdown HTTP server
	if a.server != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := a.server.Shutdown(ctx); err != nil {
			errs = append(errs, fmt.Errorf("HTTP server shutdown error: %w", err))
		}
	}

	// Unload eBPF program
	if a.responder != nil {
		if err := a.responder.Unload(); err != nil {
			errs = append(errs, fmt.Errorf("eBPF unload error: %w", err))
		}
	}

	// Wait for goroutines to finish
	a.wg.Wait()

	if len(errs) > 0 {
		return fmt.Errorf("shutdown errors: %v", errs)
	}

	a.logger.Info("Application shutdown completed")
	return nil
}

// IsRunning returns whether the application is running
func (a *Application) IsRunning() bool {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return a.running
}

// GetStats returns current packet processing statistics
func (a *Application) GetStats() (*types.PacketStats, error) {
	if a.responder == nil {
		return nil, fmt.Errorf("ICMP responder not initialized")
	}
	return a.responder.GetStats()
}

// handleDebugLogs handles debug logs endpoint
func (a *Application) handleDebugLogs(w http.ResponseWriter, r *http.Request) {
	if a.responder == nil {
		http.Error(w, "ICMP responder not initialized", http.StatusServiceUnavailable)
		return
	}

	logs, err := a.responder.GetDebugLogs()
	if err != nil {
		http.Error(w, fmt.Sprintf("Failed to get debug logs: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	if err := json.NewEncoder(w).Encode(logs); err != nil {
		a.logger.Error("Failed to encode debug logs response", zap.Error(err))
	}
}

// GetInterface returns network interface information
func (a *Application) GetInterface() *types.NetworkInterface {
	if a.responder == nil {
		return nil
	}
	return a.responder.GetInterface()
}

// SetEnabled enables or disables the ICMP responder
func (a *Application) SetEnabled(enabled bool) error {
	if a.responder == nil {
		return fmt.Errorf("ICMP responder not initialized")
	}
	return a.responder.SetEnabled(enabled)
}

// IsEnabled returns whether the ICMP responder is enabled
func (a *Application) IsEnabled() bool {
	if a.responder == nil {
		return false
	}
	return a.responder.IsEnabled()
}
