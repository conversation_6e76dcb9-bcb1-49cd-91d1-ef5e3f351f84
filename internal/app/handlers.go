package app

import (
	"encoding/json"
	"net/http"
	"time"

	"go.uber.org/zap"
)

// HealthResponse represents health check response
type HealthResponse struct {
	Status    string    `json:"status"`
	Timestamp time.Time `json:"timestamp"`
	Version   string    `json:"version"`
}

// StatusResponse represents status response
type StatusResponse struct {
	Running   bool                   `json:"running"`
	Enabled   bool                   `json:"enabled"`
	Interface map[string]interface{} `json:"interface"`
	Timestamp time.Time              `json:"timestamp"`
}

// ConfigResponse represents configuration response
type ConfigResponse struct {
	Server    map[string]interface{} `json:"server"`
	ICMP      map[string]interface{} `json:"icmp"`
	Log       map[string]interface{} `json:"log"`
	Timestamp time.Time              `json:"timestamp"`
}

// ErrorResponse represents error response
type ErrorResponse struct {
	Error     string    `json:"error"`
	Timestamp time.Time `json:"timestamp"`
}

// handleHealth handles health check requests
func (a *Application) handleHealth(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		a.writeError(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := HealthResponse{
		Status:    "ok",
		Timestamp: time.Now(),
		Version:   "dev", // TODO: Get from build info
	}

	a.writeJSON(w, response, http.StatusOK)
}

// handleStatus handles status requests
func (a *Application) handleStatus(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		a.writeError(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	iface := a.GetInterface()
	var ifaceData map[string]interface{}
	if iface != nil {
		ifaceData = map[string]interface{}{
			"name":          iface.Name,
			"index":         iface.Index,
			"mtu":           iface.MTU,
			"hardware_addr": iface.HardwareAddr.String(),
			"flags":         iface.Flags.String(),
		}
	}

	response := StatusResponse{
		Running:   a.IsRunning(),
		Enabled:   a.IsEnabled(),
		Interface: ifaceData,
		Timestamp: time.Now(),
	}

	a.writeJSON(w, response, http.StatusOK)
}

// handleStats handles statistics requests
func (a *Application) handleStats(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		a.writeError(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	stats, err := a.GetStats()
	if err != nil {
		a.logger.Error("Failed to get statistics", zap.Error(err))
		a.writeError(w, "Failed to get statistics", http.StatusInternalServerError)
		return
	}

	a.writeJSON(w, stats, http.StatusOK)
}

// handleConfig handles configuration requests
func (a *Application) handleConfig(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodGet {
		a.writeError(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	response := ConfigResponse{
		Server: map[string]interface{}{
			"host":          a.config.Server.Host,
			"port":          a.config.Server.Port,
			"read_timeout":  a.config.Server.ReadTimeout,
			"write_timeout": a.config.Server.WriteTimeout,
		},
		ICMP: map[string]interface{}{
			"interface": a.config.ICMP.Interface,
			"enabled":   a.config.ICMP.Enabled,
			"ttl":       a.config.ICMP.TTL,
			"max_size":  a.config.ICMP.MaxSize,
		},
		Log: map[string]interface{}{
			"level":       a.config.Log.Level,
			"format":      a.config.Log.Format,
			"output":      a.config.Log.Output,
			"max_size":    a.config.Log.MaxSize,
			"max_backups": a.config.Log.MaxBackups,
			"max_age":     a.config.Log.MaxAge,
			"compress":    a.config.Log.Compress,
		},
		Timestamp: time.Now(),
	}

	a.writeJSON(w, response, http.StatusOK)
}

// handleEnable handles enable requests
func (a *Application) handleEnable(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		a.writeError(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	if err := a.SetEnabled(true); err != nil {
		a.logger.Error("Failed to enable ICMP responder", zap.Error(err))
		a.writeError(w, "Failed to enable ICMP responder", http.StatusInternalServerError)
		return
	}

	a.logger.Info("ICMP responder enabled via API")
	a.writeJSON(w, map[string]interface{}{
		"message":   "ICMP responder enabled",
		"enabled":   true,
		"timestamp": time.Now(),
	}, http.StatusOK)
}

// handleDisable handles disable requests
func (a *Application) handleDisable(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		a.writeError(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	if err := a.SetEnabled(false); err != nil {
		a.logger.Error("Failed to disable ICMP responder", zap.Error(err))
		a.writeError(w, "Failed to disable ICMP responder", http.StatusInternalServerError)
		return
	}

	a.logger.Info("ICMP responder disabled via API")
	a.writeJSON(w, map[string]interface{}{
		"message":   "ICMP responder disabled",
		"enabled":   false,
		"timestamp": time.Now(),
	}, http.StatusOK)
}

// writeJSON writes JSON response
func (a *Application) writeJSON(w http.ResponseWriter, data interface{}, statusCode int) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(statusCode)

	if err := json.NewEncoder(w).Encode(data); err != nil {
		a.logger.Error("Failed to encode JSON response", zap.Error(err))
	}
}

// writeError writes error response
func (a *Application) writeError(w http.ResponseWriter, message string, statusCode int) {
	response := ErrorResponse{
		Error:     message,
		Timestamp: time.Now(),
	}

	a.writeJSON(w, response, statusCode)
}
