package logger

import (
	"bytes"
	"encoding/json"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

func TestNew(t *testing.T) {
	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid console config",
			config: &Config{
				Level:  "info",
				Format: "console",
				Output: "stdout",
			},
			wantErr: false,
		},
		{
			name: "valid json config",
			config: &Config{
				Level:  "debug",
				Format: "json",
				Output: "stdout",
			},
			wantErr: false,
		},
		{
			name: "invalid log level",
			config: &Config{
				Level:  "invalid",
				Format: "json",
				Output: "stdout",
			},
			wantErr: true,
		},
		{
			name:    "nil config",
			config:  nil,
			wantErr: false, // Should create development logger
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			logger, err := New(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("New() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if logger == nil && !tt.wantErr {
				t.Error("Expected logger to be created, got nil")
			}
		})
	}
}

func TestNewWithFileOutput(t *testing.T) {
	tmpDir := t.TempDir()
	logFile := filepath.Join(tmpDir, "test.log")

	config := &Config{
		Level:      "info",
		Format:     "json",
		Output:     logFile,
		MaxSize:    10,
		MaxBackups: 3,
		MaxAge:     7,
		Compress:   true,
	}

	logger, err := New(config)
	if err != nil {
		t.Fatalf("Failed to create logger with file output: %v", err)
	}

	if logger == nil {
		t.Fatal("Expected logger to be created, got nil")
	}

	// Test logging to file
	logger.Info("test message")
	logger.Sync()

	// Check if file was created
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		t.Error("Expected log file to be created")
	}
}

func TestLogLevels(t *testing.T) {
	var buf bytes.Buffer

	// Create a logger that writes to buffer
	encoderConfig := zap.NewProductionEncoderConfig()
	encoder := zapcore.NewJSONEncoder(encoderConfig)
	writeSyncer := zapcore.AddSync(&buf)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.DebugLevel)
	logger := zap.New(core)

	// Test different log levels
	logger.Debug("debug message")
	logger.Info("info message")
	logger.Warn("warn message")
	logger.Error("error message")

	logger.Sync()

	output := buf.String()
	lines := strings.Split(strings.TrimSpace(output), "\n")

	if len(lines) != 4 {
		t.Errorf("Expected 4 log lines, got %d", len(lines))
	}

	// Check each log level
	levels := []string{"debug", "info", "warn", "error"}
	for i, line := range lines {
		var logEntry map[string]interface{}
		if err := json.Unmarshal([]byte(line), &logEntry); err != nil {
			t.Errorf("Failed to parse log line %d: %v", i, err)
			continue
		}

		if logEntry["level"] != levels[i] {
			t.Errorf("Expected level '%s', got '%s'", levels[i], logEntry["level"])
		}
	}
}

func TestNewDefault(t *testing.T) {
	logger := NewDefault()
	if logger == nil {
		t.Fatal("Expected default logger to be created, got nil")
	}

	// Test that it can log without errors
	logger.Info("test message")
	logger.Sync()
}

func TestNewProduction(t *testing.T) {
	logger := NewProduction()
	if logger == nil {
		t.Fatal("Expected production logger to be created, got nil")
	}

	// Test that it can log without errors
	logger.Info("test message")
	logger.Sync()
}

func TestWithFields(t *testing.T) {
	var buf bytes.Buffer

	encoderConfig := zap.NewProductionEncoderConfig()
	encoder := zapcore.NewJSONEncoder(encoderConfig)
	writeSyncer := zapcore.AddSync(&buf)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)
	logger := zap.New(core)

	// Create logger with fields
	loggerWithFields := WithFields(logger, zap.String("key1", "value1"), zap.Int("key2", 42))
	loggerWithFields.Info("test message")
	loggerWithFields.Sync()

	output := buf.String()
	var logEntry map[string]interface{}
	if err := json.Unmarshal([]byte(strings.TrimSpace(output)), &logEntry); err != nil {
		t.Fatalf("Failed to parse log entry: %v", err)
	}

	if logEntry["key1"] != "value1" {
		t.Errorf("Expected key1 'value1', got '%v'", logEntry["key1"])
	}

	if logEntry["key2"] != float64(42) { // JSON numbers are float64
		t.Errorf("Expected key2 42, got %v", logEntry["key2"])
	}
}

func TestWithComponent(t *testing.T) {
	var buf bytes.Buffer

	encoderConfig := zap.NewProductionEncoderConfig()
	encoder := zapcore.NewJSONEncoder(encoderConfig)
	writeSyncer := zapcore.AddSync(&buf)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)
	logger := zap.New(core)

	// Create logger with component
	componentLogger := WithComponent(logger, "test-component")
	componentLogger.Info("test message")
	componentLogger.Sync()

	output := buf.String()
	var logEntry map[string]interface{}
	if err := json.Unmarshal([]byte(strings.TrimSpace(output)), &logEntry); err != nil {
		t.Fatalf("Failed to parse log entry: %v", err)
	}

	if logEntry["component"] != "test-component" {
		t.Errorf("Expected component 'test-component', got '%v'", logEntry["component"])
	}
}

func TestWithRequestID(t *testing.T) {
	var buf bytes.Buffer

	encoderConfig := zap.NewProductionEncoderConfig()
	encoder := zapcore.NewJSONEncoder(encoderConfig)
	writeSyncer := zapcore.AddSync(&buf)
	core := zapcore.NewCore(encoder, writeSyncer, zapcore.InfoLevel)
	logger := zap.New(core)

	// Create logger with request ID
	requestLogger := WithRequestID(logger, "req-123")
	requestLogger.Info("test message")
	requestLogger.Sync()

	output := buf.String()
	var logEntry map[string]interface{}
	if err := json.Unmarshal([]byte(strings.TrimSpace(output)), &logEntry); err != nil {
		t.Fatalf("Failed to parse log entry: %v", err)
	}

	if logEntry["request_id"] != "req-123" {
		t.Errorf("Expected request_id 'req-123', got '%v'", logEntry["request_id"])
	}
}

func TestSync(t *testing.T) {
	logger := NewDefault()

	// Test sync with valid logger (ignore sync errors for stdout/stderr)
	err := Sync(logger)
	if err != nil {
		t.Logf("Sync() returned expected error for stdout: %v", err)
	}

	// Test sync with nil logger
	if err := Sync(nil); err != nil {
		t.Errorf("Sync(nil) should not return error, got %v", err)
	}
}
