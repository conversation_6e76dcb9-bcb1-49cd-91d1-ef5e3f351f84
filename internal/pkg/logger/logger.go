package logger

import (
	"fmt"
	"os"
	"path/filepath"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

// Config represents logger configuration
type Config struct {
	Level      string `json:"level" yaml:"level"`
	Format     string `json:"format" yaml:"format"`
	Output     string `json:"output" yaml:"output"`
	MaxSize    int    `json:"max_size" yaml:"max_size"`
	MaxBackups int    `json:"max_backups" yaml:"max_backups"`
	MaxAge     int    `json:"max_age" yaml:"max_age"`
	Compress   bool   `json:"compress" yaml:"compress"`
}

// New creates a new zap logger based on configuration
func New(config *Config) (*zap.Logger, error) {
	if config == nil {
		return zap.NewDevelopment()
	}

	// Parse log level
	level, err := zapcore.ParseLevel(config.Level)
	if err != nil {
		return nil, fmt.Errorf("invalid log level %s: %w", config.Level, err)
	}

	// Create encoder config
	var encoderConfig zapcore.EncoderConfig
	if config.Format == "console" {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	} else {
		encoderConfig = zap.NewProductionEncoderConfig()
		encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
	}

	// Create encoder
	var encoder zapcore.Encoder
	if config.Format == "console" {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	}

	// Create writer syncer
	var writeSyncer zapcore.WriteSyncer
	if config.Output == "stdout" || config.Output == "" {
		writeSyncer = zapcore.AddSync(os.Stdout)
	} else if config.Output == "stderr" {
		writeSyncer = zapcore.AddSync(os.Stderr)
	} else {
		// File output with rotation
		if err := os.MkdirAll(filepath.Dir(config.Output), 0755); err != nil {
			return nil, fmt.Errorf("failed to create log directory: %w", err)
		}

		lumberjackLogger := &lumberjack.Logger{
			Filename:   config.Output,
			MaxSize:    config.MaxSize,    // MB
			MaxBackups: config.MaxBackups,
			MaxAge:     config.MaxAge,     // days
			Compress:   config.Compress,
		}
		writeSyncer = zapcore.AddSync(lumberjackLogger)
	}

	// Create core
	core := zapcore.NewCore(encoder, writeSyncer, level)

	// Create logger with caller info
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return logger, nil
}

// NewDefault creates a default logger for development
func NewDefault() *zap.Logger {
	config := &Config{
		Level:  "info",
		Format: "console",
		Output: "stdout",
	}

	logger, err := New(config)
	if err != nil {
		// Fallback to development logger
		logger, _ = zap.NewDevelopment()
	}

	return logger
}

// NewProduction creates a production logger with JSON format
func NewProduction() *zap.Logger {
	config := &Config{
		Level:      "info",
		Format:     "json",
		Output:     "stdout",
		MaxSize:    100,
		MaxBackups: 3,
		MaxAge:     28,
		Compress:   true,
	}

	logger, err := New(config)
	if err != nil {
		// Fallback to production logger
		logger, _ = zap.NewProduction()
	}

	return logger
}

// WithFields creates a logger with predefined fields
func WithFields(logger *zap.Logger, fields ...zap.Field) *zap.Logger {
	return logger.With(fields...)
}

// WithComponent creates a logger with component field
func WithComponent(logger *zap.Logger, component string) *zap.Logger {
	return logger.With(zap.String("component", component))
}

// WithRequestID creates a logger with request ID field
func WithRequestID(logger *zap.Logger, requestID string) *zap.Logger {
	return logger.With(zap.String("request_id", requestID))
}

// Sync flushes any buffered log entries
func Sync(logger *zap.Logger) error {
	if logger == nil {
		return nil
	}
	return logger.Sync()
}
