package ebpf

import (
	"fmt"
	"net"
	"runtime"
	"time"

	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/rlimit"
	"go.uber.org/zap"

	"talusaegis/pkg/types"
)

//go:generate go run github.com/cilium/ebpf/cmd/bpf2go -cc clang -cflags "-O2 -g -Wall -Werror" -strip strip icmp_responder c/icmp_responder.c

// ICMPResponder manages the eBPF program for ICMP response
type ICMPResponder struct {
	logger  *zap.Logger
	objs    *icmp_responderObjects
	link    link.Link
	iface   *net.Interface
	stats   *types.PacketStats
	enabled bool
}

// Config represents the configuration for ICMP responder
type Config struct {
	Interface string `json:"interface" yaml:"interface"`
	Enabled   bool   `json:"enabled" yaml:"enabled"`
	TTL       uint32 `json:"ttl" yaml:"ttl"`
	MaxSize   uint32 `json:"max_size" yaml:"max_size"`
}

// NewICMPResponder creates a new ICMP responder instance
func NewICMPResponder(logger *zap.Logger, config *Config) (*ICMPResponder, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// Find network interface
	iface, err := net.InterfaceByName(config.Interface)
	if err != nil {
		return nil, fmt.Errorf("failed to find interface %s: %w", config.Interface, err)
	}

	responder := &ICMPResponder{
		logger:  logger,
		iface:   iface,
		enabled: config.Enabled,
		stats: &types.PacketStats{
			LastUpdated: time.Now(),
		},
	}

	return responder, nil
}

// Load loads and attaches the eBPF program
func (r *ICMPResponder) Load() error {
	// Check if running on Linux
	if runtime.GOOS != "linux" {
		r.logger.Warn("eBPF is not supported on non-Linux platforms, using mock implementation")
		return fmt.Errorf("eBPF not supported on %s", runtime.GOOS)
	}

	// Remove memory limit for eBPF
	if err := rlimit.RemoveMemlock(); err != nil {
		return fmt.Errorf("failed to remove memlock limit: %w", err)
	}

	// Load pre-compiled programs and maps into the kernel
	objs := icmp_responderObjects{}
	if err := loadIcmp_responderObjects(&objs, nil); err != nil {
		return fmt.Errorf("failed to load eBPF objects: %w", err)
	}
	r.objs = &objs

	// Initialize configuration in eBPF map
	if err := r.updateConfig(); err != nil {
		r.objs.Close()
		return fmt.Errorf("failed to update config: %w", err)
	}

	// Attach the program to the network interface
	l, err := link.AttachXDP(link.XDPOptions{
		Program:   r.objs.IcmpResponderMain,
		Interface: r.iface.Index,
		Flags:     link.XDPGenericMode, // Use generic mode for compatibility
	})
	if err != nil {
		r.objs.Close()
		return fmt.Errorf("failed to attach XDP program: %w", err)
	}
	r.link = l

	r.logger.Info("eBPF ICMP responder loaded successfully",
		zap.String("interface", r.iface.Name),
		zap.Int("interface_index", r.iface.Index),
		zap.Bool("enabled", r.enabled))

	return nil
}

// Unload detaches and unloads the eBPF program
func (r *ICMPResponder) Unload() error {
	var errs []error

	if r.link != nil {
		if err := r.link.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close link: %w", err))
		}
		r.link = nil
	}

	if r.objs != nil {
		if err := r.objs.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close objects: %w", err))
		}
		r.objs = nil
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors during unload: %v", errs)
	}

	r.logger.Info("eBPF ICMP responder unloaded successfully")
	return nil
}

// updateConfig updates the eBPF configuration map
func (r *ICMPResponder) updateConfig() error {
	if r.objs == nil {
		return fmt.Errorf("eBPF objects not loaded")
	}

	// Update enabled status
	key := uint32(0) // CONFIG_ENABLED
	value := uint32(0)
	if r.enabled {
		value = 1
	}
	if err := r.objs.ConfigMap.Put(key, value); err != nil {
		return fmt.Errorf("failed to update enabled config: %w", err)
	}

	// Update TTL
	key = uint32(1)    // CONFIG_TTL
	value = uint32(64) // Default TTL
	if err := r.objs.ConfigMap.Put(key, value); err != nil {
		return fmt.Errorf("failed to update TTL config: %w", err)
	}

	// Update max size
	key = uint32(2)      // CONFIG_MAX_SIZE
	value = uint32(1500) // Default MTU
	if err := r.objs.ConfigMap.Put(key, value); err != nil {
		return fmt.Errorf("failed to update max size config: %w", err)
	}

	return nil
}

// GetStats retrieves current packet processing statistics
func (r *ICMPResponder) GetStats() (*types.PacketStats, error) {
	if r.objs == nil {
		return nil, fmt.Errorf("eBPF objects not loaded")
	}

	stats := &types.PacketStats{
		LastUpdated: time.Now(),
	}

	// Read statistics from eBPF map
	var key, value uint32

	// Received packets
	key = 0
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Received = uint64(value)
	}

	// Processed packets
	key = 1
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Processed = uint64(value)
	}

	// Responded packets
	key = 2
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Responded = uint64(value)
	}

	// Dropped packets
	key = 3
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Dropped = uint64(value)
	}

	// Error packets
	key = 4
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Errors = uint64(value)
	}

	r.stats = stats
	return stats, nil
}

// SetEnabled enables or disables the ICMP responder
func (r *ICMPResponder) SetEnabled(enabled bool) error {
	r.enabled = enabled

	if r.objs != nil {
		key := uint32(0) // CONFIG_ENABLED
		value := uint32(0)
		if enabled {
			value = 1
		}
		if err := r.objs.ConfigMap.Put(key, value); err != nil {
			return fmt.Errorf("failed to update enabled status: %w", err)
		}
	}

	r.logger.Info("ICMP responder status updated", zap.Bool("enabled", enabled))
	return nil
}

// IsEnabled returns whether the ICMP responder is enabled
func (r *ICMPResponder) IsEnabled() bool {
	return r.enabled
}

// GetInterface returns the network interface information
func (r *ICMPResponder) GetInterface() *types.NetworkInterface {
	if r.iface == nil {
		return nil
	}

	addrs, _ := r.iface.Addrs()

	return &types.NetworkInterface{
		Name:         r.iface.Name,
		Index:        r.iface.Index,
		MTU:          r.iface.MTU,
		HardwareAddr: r.iface.HardwareAddr,
		Addresses:    addrs,
		Flags:        r.iface.Flags,
	}
}
