package ebpf

import (
	"fmt"
	"net"
	"runtime"
	"time"

	"github.com/cilium/ebpf/link"
	"github.com/cilium/ebpf/rlimit"
	"go.uber.org/zap"

	"talusaegis/pkg/types"
)

//go:generate go run github.com/cilium/ebpf/cmd/bpf2go -cc clang -cflags "-O2 -g -Wall -Werror" icmp_responder c/icmp_responder.c

// ICMPResponder manages the eBPF program for ICMP response
type ICMPResponder struct {
	logger  *zap.Logger
	config  *Config
	objs    *icmp_responderObjects
	link    link.Link
	iface   *net.Interface
	stats   *types.PacketStats
	enabled bool
}

// Config represents the configuration for ICMP responder
type Config struct {
	Interface string `json:"interface" yaml:"interface"`
	Enabled   bool   `json:"enabled" yaml:"enabled"`
	TTL       uint32 `json:"ttl" yaml:"ttl"`
	MaxSize   uint32 `json:"max_size" yaml:"max_size"`
	Debug     bool   `json:"debug" yaml:"debug"`
}

// NewICMPResponder creates a new ICMP responder instance
func NewICMPResponder(logger *zap.Logger, config *Config) (*ICMPResponder, error) {
	if logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	if config == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	// Find network interface
	iface, err := net.InterfaceByName(config.Interface)
	if err != nil {
		return nil, fmt.Errorf("failed to find interface %s: %w", config.Interface, err)
	}

	responder := &ICMPResponder{
		logger:  logger,
		config:  config,
		iface:   iface,
		enabled: config.Enabled,
		stats: &types.PacketStats{
			LastUpdated: time.Now(),
		},
	}

	return responder, nil
}

// Load loads and attaches the eBPF program
func (r *ICMPResponder) Load() error {
	// Check if running on Linux
	if runtime.GOOS != "linux" {
		r.logger.Warn("eBPF is not supported on non-Linux platforms, using mock implementation")
		return fmt.Errorf("eBPF not supported on %s", runtime.GOOS)
	}

	// Remove memory limit for eBPF
	if err := rlimit.RemoveMemlock(); err != nil {
		return fmt.Errorf("failed to remove memlock limit: %w", err)
	}

	// Load pre-compiled programs and maps into the kernel
	objs := icmp_responderObjects{}
	if err := loadIcmp_responderObjects(&objs, nil); err != nil {
		return fmt.Errorf("failed to load eBPF objects: %w", err)
	}
	r.objs = &objs

	// Initialize configuration in eBPF map
	if err := r.updateConfig(); err != nil {
		r.objs.Close()
		return fmt.Errorf("failed to update config: %w", err)
	}

	// Attach the program to the network interface
	l, err := link.AttachXDP(link.XDPOptions{
		Program:   r.objs.IcmpResponderMain,
		Interface: r.iface.Index,
		Flags:     link.XDPGenericMode, // Use generic mode for compatibility
	})
	if err != nil {
		r.objs.Close()
		return fmt.Errorf("failed to attach XDP program: %w", err)
	}
	r.link = l

	r.logger.Info("eBPF ICMP responder loaded successfully",
		zap.String("interface", r.iface.Name),
		zap.Int("interface_index", r.iface.Index),
		zap.Bool("enabled", r.enabled))

	return nil
}

// Unload detaches and unloads the eBPF program
func (r *ICMPResponder) Unload() error {
	var errs []error

	if r.link != nil {
		if err := r.link.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close link: %w", err))
		}
		r.link = nil
	}

	if r.objs != nil {
		if err := r.objs.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close objects: %w", err))
		}
		r.objs = nil
	}

	if len(errs) > 0 {
		return fmt.Errorf("errors during unload: %v", errs)
	}

	r.logger.Info("eBPF ICMP responder unloaded successfully")
	return nil
}

// updateConfig updates the eBPF configuration map
func (r *ICMPResponder) updateConfig() error {
	if r.objs == nil {
		return fmt.Errorf("eBPF objects not loaded")
	}

	// Update enabled status
	key := uint32(0) // CONFIG_ENABLED
	value := uint32(0)
	if r.enabled {
		value = 1
	}
	if err := r.objs.ConfigMap.Put(key, value); err != nil {
		return fmt.Errorf("failed to update enabled config: %w", err)
	}

	// Update TTL
	key = uint32(1)    // CONFIG_TTL
	value = uint32(64) // Default TTL
	if err := r.objs.ConfigMap.Put(key, value); err != nil {
		return fmt.Errorf("failed to update TTL config: %w", err)
	}

	// Update max size
	key = uint32(2)      // CONFIG_MAX_SIZE
	value = uint32(1500) // Default MTU
	if err := r.objs.ConfigMap.Put(key, value); err != nil {
		return fmt.Errorf("failed to update max size config: %w", err)
	}

	// Update interface IP address
	interfaceIP, err := r.getInterfaceIP()
	if err != nil {
		return fmt.Errorf("failed to get interface IP: %w", err)
	}
	key = uint32(3) // CONFIG_INTERFACE_IP
	if err := r.objs.ConfigMap.Put(key, interfaceIP); err != nil {
		return fmt.Errorf("failed to update interface IP config: %w", err)
	}

	// Update debug mode
	debugValue := uint32(0)
	if r.config.Debug {
		debugValue = 1
	}
	key = uint32(4) // CONFIG_DEBUG
	if err := r.objs.ConfigMap.Put(key, debugValue); err != nil {
		return fmt.Errorf("failed to update debug config: %w", err)
	}

	return nil
}

// GetStats retrieves current packet processing statistics
func (r *ICMPResponder) GetStats() (*types.PacketStats, error) {
	if r.objs == nil {
		return nil, fmt.Errorf("eBPF objects not loaded")
	}

	stats := &types.PacketStats{
		LastUpdated: time.Now(),
	}

	// Read statistics from eBPF map
	var key uint32
	var value uint64

	// Received packets
	key = 0
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Received = value
	}

	// Processed packets
	key = 1
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Processed = value
	}

	// Responded packets
	key = 2
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Responded = value
	}

	// Dropped packets
	key = 3
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Dropped = value
	}

	// Error packets
	key = 4
	if err := r.objs.StatsMap.Lookup(key, &value); err == nil {
		stats.Errors = value
	}

	r.stats = stats
	return stats, nil
}

// SetEnabled enables or disables the ICMP responder
func (r *ICMPResponder) SetEnabled(enabled bool) error {
	r.enabled = enabled

	if r.objs != nil {
		key := uint32(0) // CONFIG_ENABLED
		value := uint32(0)
		if enabled {
			value = 1
		}
		if err := r.objs.ConfigMap.Put(key, value); err != nil {
			return fmt.Errorf("failed to update enabled status: %w", err)
		}
	}

	r.logger.Info("ICMP responder status updated", zap.Bool("enabled", enabled))
	return nil
}

// getInterfaceIP retrieves the IP address of the network interface
func (r *ICMPResponder) getInterfaceIP() (uint32, error) {
	addrs, err := r.iface.Addrs()
	if err != nil {
		return 0, fmt.Errorf("failed to get interface addresses: %w", err)
	}

	for _, addr := range addrs {
		if ipnet, ok := addr.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
			if ipv4 := ipnet.IP.To4(); ipv4 != nil {
				// Convert IP to uint32 in host byte order, then convert to network byte order
				// ipv4 is [192, 168, 15, 13] for *************
				ip := uint32(ipv4[0])<<24 | uint32(ipv4[1])<<16 | uint32(ipv4[2])<<8 | uint32(ipv4[3])
				r.logger.Info("Interface IP address",
					zap.String("ip", ipv4.String()),
					zap.Uint32("ip_uint32_host", ip),
					zap.String("ip_hex_host", fmt.Sprintf("0x%08x", ip)))
				return ip, nil
			}
		}
	}

	return 0, fmt.Errorf("no IPv4 address found on interface %s", r.iface.Name)
}

// DebugLogEntry represents a debug log entry from eBPF
type DebugLogEntry struct {
	Timestamp uint32 `json:"timestamp"`
	SrcIP     string `json:"src_ip"`
	DstIP     string `json:"dst_ip"`
	ICMPID    uint16 `json:"icmp_id"`
	ICMPSeq   uint16 `json:"icmp_seq"`
	Action    string `json:"action"`
}

// GetDebugLogs retrieves debug log entries from eBPF
func (r *ICMPResponder) GetDebugLogs() ([]DebugLogEntry, error) {
	if !r.config.Debug {
		return nil, fmt.Errorf("debug mode is not enabled")
	}

	var logs []DebugLogEntry

	// Get current debug index
	var indexKey uint32 = 0
	var currentIndex uint32
	if err := r.objs.DebugIndexMap.Lookup(indexKey, &currentIndex); err != nil {
		return logs, nil // Return empty if no entries yet
	}

	// Read up to 100 most recent entries
	maxEntries := uint32(100)
	if currentIndex < maxEntries {
		maxEntries = currentIndex
	}

	actionNames := []string{"received", "processed", "responded", "dropped", "error", "debug_interface_ip", "debug_icmp_type"}

	for i := uint32(0); i < maxEntries; i++ {
		entryIndex := (currentIndex - maxEntries + i) % 1024

		var entry struct {
			Timestamp uint32
			SrcIP     uint32
			DstIP     uint32
			ICMPID    uint16
			ICMPSeq   uint16
			Action    uint8
			Reserved  [3]uint8
		}

		if err := r.objs.DebugLogMap.Lookup(entryIndex, &entry); err != nil {
			continue
		}

		actionName := "unknown"
		if int(entry.Action) < len(actionNames) {
			actionName = actionNames[entry.Action]
		}

		logs = append(logs, DebugLogEntry{
			Timestamp: entry.Timestamp,
			SrcIP: fmt.Sprintf("%d.%d.%d.%d",
				(entry.SrcIP>>24)&0xFF, (entry.SrcIP>>16)&0xFF,
				(entry.SrcIP>>8)&0xFF, entry.SrcIP&0xFF),
			DstIP: fmt.Sprintf("%d.%d.%d.%d",
				(entry.DstIP>>24)&0xFF, (entry.DstIP>>16)&0xFF,
				(entry.DstIP>>8)&0xFF, entry.DstIP&0xFF),
			ICMPID:  entry.ICMPID,
			ICMPSeq: entry.ICMPSeq,
			Action:  actionName,
		})
	}

	return logs, nil
}

// IsEnabled returns whether the ICMP responder is enabled
func (r *ICMPResponder) IsEnabled() bool {
	return r.enabled
}

// GetInterface returns the network interface information
func (r *ICMPResponder) GetInterface() *types.NetworkInterface {
	if r.iface == nil {
		return nil
	}

	addrs, _ := r.iface.Addrs()

	return &types.NetworkInterface{
		Name:         r.iface.Name,
		Index:        r.iface.Index,
		MTU:          r.iface.MTU,
		HardwareAddr: r.iface.HardwareAddr,
		Addresses:    addrs,
		Flags:        r.iface.Flags,
	}
}

// Close cleans up eBPF resources
func (r *ICMPResponder) Close() error {
	var errs []error

	// Detach XDP program
	if r.link != nil {
		if err := r.link.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to detach XDP program: %w", err))
		}
		r.link = nil
	}

	// Close eBPF objects
	if r.objs != nil {
		if err := r.objs.Close(); err != nil {
			errs = append(errs, fmt.Errorf("failed to close eBPF objects: %w", err))
		}
		r.objs = nil
	}

	if len(errs) > 0 {
		return fmt.Errorf("cleanup errors: %v", errs)
	}

	return nil
}

// Reload reloads the eBPF program with current configuration
func (r *ICMPResponder) Reload() error {
	r.logger.Info("Reloading eBPF program")

	// Close existing resources
	if err := r.Close(); err != nil {
		r.logger.Warn("Error during cleanup before reload", zap.Error(err))
	}

	// Reload the program
	return r.Load()
}
