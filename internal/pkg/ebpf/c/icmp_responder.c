//go:build ignore

#include <linux/bpf.h>
#include <linux/if_ether.h>
#include <linux/ip.h>
#include <linux/icmp.h>
#include <linux/in.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_endian.h>

#define MAX_ENTRIES 1024

// Map to store packet statistics
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 8);
    __type(key, __u32);
    __type(value, __u64);
} stats_map SEC(".maps");

// Map to store configuration
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 8);
    __type(key, __u32);
    __type(value, __u32);
} config_map SEC(".maps");

// Debug log entry structure
struct debug_log_entry {
    __u32 timestamp;
    __u32 src_ip;
    __u32 dst_ip;
    __u16 icmp_id;
    __u16 icmp_seq;
    __u8 action;  // 0=received, 1=processed, 2=responded, 3=dropped, 4=error
    __u8 reserved[3];
};

// Map to store debug log entries (ring buffer style)
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1024);  // Store up to 1024 debug entries
    __type(key, __u32);
    __type(value, struct debug_log_entry);
} debug_log_map SEC(".maps");

// Map to store debug log index
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, __u32);
} debug_index_map SEC(".maps");

// Statistics indices
#define STAT_RECEIVED    0
#define STAT_PROCESSED   1
#define STAT_RESPONDED   2
#define STAT_DROPPED     3
#define STAT_ERRORS      4

// Configuration indices
#define CONFIG_ENABLED      0
#define CONFIG_TTL          1
#define CONFIG_MAX_SIZE     2
#define CONFIG_DEBUG        3
#define CONFIG_USE_SOURCE_IP 4
#define CONFIG_INTERFACE_IP  5

// Simple checksum calculation without bpf_csum_diff to avoid verifier issues
static __always_inline __u16 simple_csum(__u16 *data, __u32 len)
{
    __u32 sum = 0;
    __u32 words = len >> 1; // Number of 16-bit words

    // Limit to reasonable size to help verifier
    if (words > 32) words = 32;

    // Manually unroll for small common cases
    if (words >= 1) sum += data[0];
    if (words >= 2) sum += data[1];
    if (words >= 3) sum += data[2];
    if (words >= 4) sum += data[3];
    if (words >= 5) sum += data[4];
    if (words >= 6) sum += data[5];
    if (words >= 7) sum += data[6];
    if (words >= 8) sum += data[7];
    if (words >= 9) sum += data[8];
    if (words >= 10) sum += data[9];

    // For IP header (10 words), this is sufficient
    // For ICMP, we'll handle the basic header

    // Fold the checksum
    while (sum >> 16) {
        sum = (sum & 0xffff) + (sum >> 16);
    }

    return ~sum;
}

static __always_inline __u16 ipv4_csum(struct iphdr *iph)
{
    iph->check = 0;
    return simple_csum((__u16 *)iph, sizeof(struct iphdr));
}

static __always_inline __u16 icmp_csum(struct icmphdr *icmph, __u32 len)
{
    icmph->checksum = 0;
    // Ensure length is positive and within reasonable bounds
    if (len == 0 || len > 1500) return 0;
    return simple_csum((__u16 *)icmph, len);
}

static __always_inline void update_stats(__u32 index)
{
    __u64 *value = bpf_map_lookup_elem(&stats_map, &index);
    if (value) {
        __sync_fetch_and_add(value, 1);
    }
}

// Helper function to log debug information
static __always_inline void log_debug(__u32 src_ip, __u32 dst_ip, __u16 icmp_id, __u16 icmp_seq, __u8 action) {
    // Check if debug mode is enabled
    __u32 debug_key = CONFIG_DEBUG;
    __u32 *debug_enabled = bpf_map_lookup_elem(&config_map, &debug_key);
    if (!debug_enabled || *debug_enabled == 0) {
        return;
    }

    // Get current debug index
    __u32 index_key = 0;
    __u32 *current_index = bpf_map_lookup_elem(&debug_index_map, &index_key);
    if (!current_index) {
        return;
    }

    __u32 index = *current_index % 1024;  // Wrap around at 1024 entries

    // Create debug log entry
    struct debug_log_entry entry = {
        .timestamp = bpf_ktime_get_ns() / 1000000,  // Convert to milliseconds
        .src_ip = src_ip,
        .dst_ip = dst_ip,
        .icmp_id = icmp_id,
        .icmp_seq = icmp_seq,
        .action = action,
    };

    // Store the entry
    bpf_map_update_elem(&debug_log_map, &index, &entry, BPF_ANY);

    // Update index for next entry
    __u32 next_index = index + 1;
    bpf_map_update_elem(&debug_index_map, &index_key, &next_index, BPF_ANY);
}

static __always_inline int process_icmp_echo(struct xdp_md *ctx)
{
    void *data_end = (void *)(long)ctx->data_end;
    void *data = (void *)(long)ctx->data;
    
    struct ethhdr *eth = data;
    if ((void *)(eth + 1) > data_end)
        return XDP_PASS;
    
    // Only process IPv4 packets
    if (eth->h_proto != bpf_htons(ETH_P_IP))
        return XDP_PASS;
    
    struct iphdr *iph = (struct iphdr *)(eth + 1);
    if ((void *)(iph + 1) > data_end)
        return XDP_PASS;
    
    // Only process ICMP packets
    if (iph->protocol != IPPROTO_ICMP)
        return XDP_PASS;
    
    // Calculate ICMP header position with bounds checking
    __u32 ip_hdr_len = iph->ihl * 4;
    if (ip_hdr_len < 20 || ip_hdr_len > 60)
        return XDP_PASS;

    struct icmphdr *icmph = (struct icmphdr *)((char *)iph + ip_hdr_len);
    if ((void *)(icmph + 1) > data_end)
        return XDP_PASS;

    // Ensure we have enough space for ICMP header fields we'll access
    if ((void *)icmph + sizeof(struct icmphdr) > data_end)
        return XDP_PASS;
    
    update_stats(STAT_RECEIVED);

    // Log packet received
    log_debug(bpf_ntohl(iph->saddr), bpf_ntohl(iph->daddr),
              bpf_ntohs(icmph->un.echo.id), bpf_ntohs(icmph->un.echo.sequence), 0);

    // Debug: log ICMP type for troubleshooting (store type in src_ip field)
    log_debug((__u32)icmph->type, (__u32)icmph->code, 0, 0, 6);  // action=6 for debug ICMP type

    // Only respond to ICMP Echo Request (type 8)
    if (icmph->type != ICMP_ECHO)
        return XDP_PASS;
    
    // Check if ICMP responder is enabled
    __u32 config_key = CONFIG_ENABLED;
    __u32 *enabled = bpf_map_lookup_elem(&config_map, &config_key);
    if (!enabled || *enabled == 0)
        return XDP_PASS;
    
    update_stats(STAT_PROCESSED);

    // Log packet processed
    log_debug(bpf_ntohl(iph->saddr), bpf_ntohl(iph->daddr),
              bpf_ntohs(icmph->un.echo.id), bpf_ntohs(icmph->un.echo.sequence), 1);
    
    // Swap MAC addresses
    __u8 tmp_mac[6];
    __builtin_memcpy(tmp_mac, eth->h_dest, 6);
    __builtin_memcpy(eth->h_dest, eth->h_source, 6);
    __builtin_memcpy(eth->h_source, tmp_mac, 6);
    
    // Handle IP address assignment based on configuration
    __be32 original_src = iph->saddr;
    __be32 original_dst = iph->daddr;

    // Check if we should use interface IP as source
    config_key = CONFIG_USE_SOURCE_IP;
    __u32 *use_source_ip = bpf_map_lookup_elem(&config_map, &config_key);

    if (use_source_ip && *use_source_ip) {
        // Use interface IP as source (compatibility mode)
        config_key = CONFIG_INTERFACE_IP;
        __u32 *interface_ip = bpf_map_lookup_elem(&config_map, &config_key);
        if (interface_ip && *interface_ip != 0) {
            iph->saddr = bpf_htonl(*interface_ip);  // Use interface IP as source
        } else {
            // Fallback: use original destination as source
            iph->saddr = original_dst;
        }
    } else {
        // Use original destination IP as source (IP spoofing/impersonation mode)
        iph->saddr = original_dst;
    }

    // Always send back to original sender
    iph->daddr = original_src;

    
    // Update TTL from configuration
    config_key = CONFIG_TTL;
    __u32 *ttl = bpf_map_lookup_elem(&config_map, &config_key);
    if (ttl && *ttl > 0)
        iph->ttl = *ttl;
    else
        iph->ttl = 64; // Default TTL
    
    // Recalculate IP checksum
    iph->check = ipv4_csum(iph);
    
    // Change ICMP type from Echo Request (8) to Echo Reply (0)
    icmph->type = ICMP_ECHOREPLY;
    
    // Calculate ICMP payload length with bounds checking
    __u16 total_len = bpf_ntohs(iph->tot_len);

    // Ensure we have valid lengths
    if (total_len < ip_hdr_len || ip_hdr_len < 20 || total_len > 1500) {
        update_stats(STAT_ERRORS);
        return XDP_DROP;
    }

    __u32 icmp_len = total_len - ip_hdr_len;

    // Additional bounds check for ICMP length
    if (icmp_len < sizeof(struct icmphdr) || icmp_len > 1480) {
        update_stats(STAT_ERRORS);
        return XDP_DROP;
    }

    // Ensure ICMP data doesn't exceed packet boundary
    if ((void *)icmph + icmp_len > data_end) {
        update_stats(STAT_ERRORS);
        return XDP_DROP;
    }

    // Recalculate ICMP checksum (only for the header part to be safe)
    icmph->checksum = 0; // Clear checksum before calculation
    icmph->checksum = icmp_csum(icmph, sizeof(struct icmphdr));
    
    update_stats(STAT_RESPONDED);

    // Log packet responded (after IP swap: saddr=original_dst, daddr=original_src)
    log_debug(bpf_ntohl(iph->saddr), bpf_ntohl(iph->daddr),
              bpf_ntohs(icmph->un.echo.id), bpf_ntohs(icmph->un.echo.sequence), 2);

    // Return the packet back to the same interface
    return XDP_TX;
}

SEC("xdp")
int icmp_responder_main(struct xdp_md *ctx)
{
    return process_icmp_echo(ctx);
}

char _license[] SEC("license") = "GPL";
