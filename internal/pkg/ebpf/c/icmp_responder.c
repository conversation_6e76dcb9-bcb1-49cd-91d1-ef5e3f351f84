//go:build ignore

#include <linux/bpf.h>
#include <linux/if_ether.h>
#include <linux/ip.h>
#include <linux/icmp.h>
#include <linux/in.h>
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_endian.h>

#define MAX_ENTRIES 1024

// Map to store packet statistics
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 8);
    __type(key, __u32);
    __type(value, __u64);
} stats_map SEC(".maps");

// Map to store configuration
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 4);
    __type(key, __u32);
    __type(value, __u32);
} config_map SEC(".maps");

// Statistics indices
#define STAT_RECEIVED    0
#define STAT_PROCESSED   1
#define STAT_RESPONDED   2
#define STAT_DROPPED     3
#define STAT_ERRORS      4

// Configuration indices
#define CONFIG_ENABLED   0
#define CONFIG_TTL       1
#define CONFIG_MAX_SIZE  2

static __always_inline __u16 csum_fold_helper(__u64 csum)
{
    int i;
#pragma unroll
    for (i = 0; i < 4; i++) {
        if (csum >> 16)
            csum = (csum & 0xffff) + (csum >> 16);
    }
    return ~csum;
}

static __always_inline __u16 ipv4_csum(struct iphdr *iph)
{
    iph->check = 0;
    unsigned long long csum = bpf_csum_diff(0, 0, (unsigned int *)iph, sizeof(struct iphdr), 0);
    return csum_fold_helper(csum);
}

static __always_inline __u16 icmp_csum(struct icmphdr *icmph, __u32 len)
{
    icmph->checksum = 0;
    unsigned long long csum = bpf_csum_diff(0, 0, (unsigned int *)icmph, len, 0);
    return csum_fold_helper(csum);
}

static __always_inline void update_stats(__u32 index)
{
    __u64 *value = bpf_map_lookup_elem(&stats_map, &index);
    if (value) {
        __sync_fetch_and_add(value, 1);
    }
}

static __always_inline int process_icmp_echo(struct xdp_md *ctx)
{
    void *data_end = (void *)(long)ctx->data_end;
    void *data = (void *)(long)ctx->data;
    
    struct ethhdr *eth = data;
    if ((void *)(eth + 1) > data_end)
        return XDP_PASS;
    
    // Only process IPv4 packets
    if (eth->h_proto != bpf_htons(ETH_P_IP))
        return XDP_PASS;
    
    struct iphdr *iph = (struct iphdr *)(eth + 1);
    if ((void *)(iph + 1) > data_end)
        return XDP_PASS;
    
    // Only process ICMP packets
    if (iph->protocol != IPPROTO_ICMP)
        return XDP_PASS;
    
    struct icmphdr *icmph = (struct icmphdr *)((char *)iph + (iph->ihl * 4));
    if ((void *)(icmph + 1) > data_end)
        return XDP_PASS;
    
    update_stats(STAT_RECEIVED);
    
    // Only respond to ICMP Echo Request (type 8)
    if (icmph->type != ICMP_ECHO)
        return XDP_PASS;
    
    // Check if ICMP responder is enabled
    __u32 config_key = CONFIG_ENABLED;
    __u32 *enabled = bpf_map_lookup_elem(&config_map, &config_key);
    if (!enabled || *enabled == 0)
        return XDP_PASS;
    
    update_stats(STAT_PROCESSED);
    
    // Swap MAC addresses
    __u8 tmp_mac[6];
    __builtin_memcpy(tmp_mac, eth->h_dest, 6);
    __builtin_memcpy(eth->h_dest, eth->h_source, 6);
    __builtin_memcpy(eth->h_source, tmp_mac, 6);
    
    // Swap IP addresses
    __be32 tmp_ip = iph->saddr;
    iph->saddr = iph->daddr;
    iph->daddr = tmp_ip;
    
    // Update TTL from configuration
    config_key = CONFIG_TTL;
    __u32 *ttl = bpf_map_lookup_elem(&config_map, &config_key);
    if (ttl && *ttl > 0)
        iph->ttl = *ttl;
    else
        iph->ttl = 64; // Default TTL
    
    // Recalculate IP checksum
    iph->check = ipv4_csum(iph);
    
    // Change ICMP type from Echo Request (8) to Echo Reply (0)
    icmph->type = ICMP_ECHOREPLY;
    
    // Calculate ICMP payload length
    __u32 icmp_len = bpf_ntohs(iph->tot_len) - (iph->ihl * 4);
    
    // Recalculate ICMP checksum
    icmph->checksum = icmp_csum(icmph, icmp_len);
    
    update_stats(STAT_RESPONDED);
    
    // Return the packet back to the same interface
    return XDP_TX;
}

SEC("xdp")
int icmp_responder_main(struct xdp_md *ctx)
{
    return process_icmp_echo(ctx);
}

char _license[] SEC("license") = "GPL";
