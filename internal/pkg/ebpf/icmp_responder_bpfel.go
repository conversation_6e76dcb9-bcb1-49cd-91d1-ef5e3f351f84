// Code generated by bpf2go; DO NOT EDIT.
//go:build 386 || amd64 || arm || arm64 || loong64 || mips64le || mipsle || ppc64le || riscv64

package ebpf

import (
	"bytes"
	_ "embed"
	"fmt"
	"io"

	"github.com/cilium/ebpf"
)

type icmp_responderDebugLogEntry struct {
	Timestamp uint32
	SrcIp     uint32
	DstIp     uint32
	IcmpId    uint16
	IcmpSeq   uint16
	Action    uint8
	Reserved  [3]uint8
}

// loadIcmp_responder returns the embedded CollectionSpec for icmp_responder.
func loadIcmp_responder() (*ebpf.CollectionSpec, error) {
	reader := bytes.NewReader(_Icmp_responderBytes)
	spec, err := ebpf.LoadCollectionSpecFromReader(reader)
	if err != nil {
		return nil, fmt.Errorf("can't load icmp_responder: %w", err)
	}

	return spec, err
}

// loadIcmp_responderObjects loads icmp_responder and converts it into a struct.
//
// The following types are suitable as obj argument:
//
//	*icmp_responderObjects
//	*icmp_responderPrograms
//	*icmp_responderMaps
//
// See ebpf.CollectionSpec.LoadAndAssign documentation for details.
func loadIcmp_responderObjects(obj interface{}, opts *ebpf.CollectionOptions) error {
	spec, err := loadIcmp_responder()
	if err != nil {
		return err
	}

	return spec.LoadAndAssign(obj, opts)
}

// icmp_responderSpecs contains maps and programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type icmp_responderSpecs struct {
	icmp_responderProgramSpecs
	icmp_responderMapSpecs
	icmp_responderVariableSpecs
}

// icmp_responderProgramSpecs contains programs before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type icmp_responderProgramSpecs struct {
	IcmpResponderMain *ebpf.ProgramSpec `ebpf:"icmp_responder_main"`
}

// icmp_responderMapSpecs contains maps before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type icmp_responderMapSpecs struct {
	ConfigMap     *ebpf.MapSpec `ebpf:"config_map"`
	DebugIndexMap *ebpf.MapSpec `ebpf:"debug_index_map"`
	DebugLogMap   *ebpf.MapSpec `ebpf:"debug_log_map"`
	StatsMap      *ebpf.MapSpec `ebpf:"stats_map"`
}

// icmp_responderVariableSpecs contains global variables before they are loaded into the kernel.
//
// It can be passed ebpf.CollectionSpec.Assign.
type icmp_responderVariableSpecs struct {
}

// icmp_responderObjects contains all objects after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderObjects or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderObjects struct {
	icmp_responderPrograms
	icmp_responderMaps
	icmp_responderVariables
}

func (o *icmp_responderObjects) Close() error {
	return _Icmp_responderClose(
		&o.icmp_responderPrograms,
		&o.icmp_responderMaps,
	)
}

// icmp_responderMaps contains all maps after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderObjects or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderMaps struct {
	ConfigMap     *ebpf.Map `ebpf:"config_map"`
	DebugIndexMap *ebpf.Map `ebpf:"debug_index_map"`
	DebugLogMap   *ebpf.Map `ebpf:"debug_log_map"`
	StatsMap      *ebpf.Map `ebpf:"stats_map"`
}

func (m *icmp_responderMaps) Close() error {
	return _Icmp_responderClose(
		m.ConfigMap,
		m.DebugIndexMap,
		m.DebugLogMap,
		m.StatsMap,
	)
}

// icmp_responderVariables contains all global variables after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderObjects or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderVariables struct {
}

// icmp_responderPrograms contains all programs after they have been loaded into the kernel.
//
// It can be passed to loadIcmp_responderObjects or ebpf.CollectionSpec.LoadAndAssign.
type icmp_responderPrograms struct {
	IcmpResponderMain *ebpf.Program `ebpf:"icmp_responder_main"`
}

func (p *icmp_responderPrograms) Close() error {
	return _Icmp_responderClose(
		p.IcmpResponderMain,
	)
}

func _Icmp_responderClose(closers ...io.Closer) error {
	for _, closer := range closers {
		if err := closer.Close(); err != nil {
			return err
		}
	}
	return nil
}

// Do not access this directly.
//
//go:embed icmp_responder_bpfel.o
var _Icmp_responderBytes []byte
