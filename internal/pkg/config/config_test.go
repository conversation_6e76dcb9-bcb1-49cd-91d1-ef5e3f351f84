package config

import (
	"os"
	"path/filepath"
	"testing"

	"go.uber.org/zap"
)

func TestNewManager(t *testing.T) {
	logger := zap.NewNop()
	manager := NewManager(logger)

	if manager == nil {
		t.Fatal("Expected manager to be created, got nil")
	}

	if manager.logger != logger {
		t.<PERSON>rror("Expected logger to be set correctly")
	}

	if manager.viper == nil {
		t.Error("Expected viper to be initialized")
	}
}

func TestSetDefaults(t *testing.T) {
	logger := zap.NewNop()
	manager := NewManager(logger)
	manager.setDefaults()

	// Test server defaults
	if manager.viper.GetString("server.host") != "0.0.0.0" {
		t.<PERSON>rrorf("Expected server.host default '0.0.0.0', got '%s'", manager.viper.GetString("server.host"))
	}

	if manager.viper.GetInt("server.port") != 8080 {
		t.<PERSON>rf("Expected server.port default 8080, got %d", manager.viper.GetInt("server.port"))
	}

	// Test ICMP defaults
	if manager.viper.GetString("icmp.interface") != "eth0" {
		t.Errorf("Expected icmp.interface default 'eth0', got '%s'", manager.viper.GetString("icmp.interface"))
	}

	if !manager.viper.GetBool("icmp.enabled") {
		t.Error("Expected icmp.enabled default true, got false")
	}

	if manager.viper.GetUint32("icmp.ttl") != 64 {
		t.Errorf("Expected icmp.ttl default 64, got %d", manager.viper.GetUint32("icmp.ttl"))
	}

	// Test log defaults
	if manager.viper.GetString("log.level") != "info" {
		t.Errorf("Expected log.level default 'info', got '%s'", manager.viper.GetString("log.level"))
	}

	if manager.viper.GetString("log.format") != "json" {
		t.Errorf("Expected log.format default 'json', got '%s'", manager.viper.GetString("log.format"))
	}
}

func TestValidateConfig(t *testing.T) {
	logger := zap.NewNop()
	manager := NewManager(logger)

	tests := []struct {
		name    string
		config  *Config
		wantErr bool
	}{
		{
			name: "valid config",
			config: &Config{
				Server: ServerConfig{
					Host: "0.0.0.0",
					Port: 8080,
				},
				ICMP: ICMPConfig{
					Interface: "eth0",
					TTL:       64,
					MaxSize:   1500,
				},
				Log: LogConfig{
					Level:  "info",
					Format: "json",
				},
			},
			wantErr: false,
		},
		{
			name: "invalid port",
			config: &Config{
				Server: ServerConfig{
					Port: 70000,
				},
				ICMP: ICMPConfig{
					Interface: "eth0",
					TTL:       64,
					MaxSize:   1500,
				},
				Log: LogConfig{
					Level:  "info",
					Format: "json",
				},
			},
			wantErr: true,
		},
		{
			name: "empty interface",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
				},
				ICMP: ICMPConfig{
					Interface: "",
					TTL:       64,
					MaxSize:   1500,
				},
				Log: LogConfig{
					Level:  "info",
					Format: "json",
				},
			},
			wantErr: true,
		},
		{
			name: "invalid TTL",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
				},
				ICMP: ICMPConfig{
					Interface: "eth0",
					TTL:       300,
					MaxSize:   1500,
				},
				Log: LogConfig{
					Level:  "info",
					Format: "json",
				},
			},
			wantErr: true,
		},
		{
			name: "invalid log level",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
				},
				ICMP: ICMPConfig{
					Interface: "eth0",
					TTL:       64,
					MaxSize:   1500,
				},
				Log: LogConfig{
					Level:  "invalid",
					Format: "json",
				},
			},
			wantErr: true,
		},
		{
			name: "invalid log format",
			config: &Config{
				Server: ServerConfig{
					Port: 8080,
				},
				ICMP: ICMPConfig{
					Interface: "eth0",
					TTL:       64,
					MaxSize:   1500,
				},
				Log: LogConfig{
					Level:  "info",
					Format: "invalid",
				},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := manager.validate(tt.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLoadConfigFromFile(t *testing.T) {
	logger := zap.NewNop()
	manager := NewManager(logger)

	// Create temporary config file
	tmpDir := t.TempDir()
	configFile := filepath.Join(tmpDir, "test_config.yaml")

	configContent := `
server:
  host: "127.0.0.1"
  port: 9090
icmp:
  interface: "lo"
  enabled: false
  ttl: 32
log:
  level: "debug"
  format: "console"
`

	if err := os.WriteFile(configFile, []byte(configContent), 0644); err != nil {
		t.Fatalf("Failed to write test config file: %v", err)
	}

	// Load configuration
	if err := manager.Load(configFile); err != nil {
		t.Fatalf("Failed to load config: %v", err)
	}

	config := manager.Get()
	if config == nil {
		t.Fatal("Expected config to be loaded, got nil")
	}

	// Verify loaded values
	if config.Server.Host != "127.0.0.1" {
		t.Errorf("Expected server.host '127.0.0.1', got '%s'", config.Server.Host)
	}

	if config.Server.Port != 9090 {
		t.Errorf("Expected server.port 9090, got %d", config.Server.Port)
	}

	if config.ICMP.Interface != "lo" {
		t.Errorf("Expected icmp.interface 'lo', got '%s'", config.ICMP.Interface)
	}

	if config.ICMP.Enabled {
		t.Error("Expected icmp.enabled false, got true")
	}

	if config.ICMP.TTL != 32 {
		t.Errorf("Expected icmp.ttl 32, got %d", config.ICMP.TTL)
	}

	if config.Log.Level != "debug" {
		t.Errorf("Expected log.level 'debug', got '%s'", config.Log.Level)
	}

	if config.Log.Format != "console" {
		t.Errorf("Expected log.format 'console', got '%s'", config.Log.Format)
	}
}

func TestGetICMPConfig(t *testing.T) {
	logger := zap.NewNop()
	manager := NewManager(logger)

	// Load defaults
	if err := manager.Load(""); err != nil {
		t.Fatalf("Failed to load default config: %v", err)
	}

	icmpConfig := manager.GetICMPConfig()
	if icmpConfig == nil {
		t.Fatal("Expected ICMP config to be returned, got nil")
	}

	if icmpConfig.Interface != "eth0" {
		t.Errorf("Expected interface 'eth0', got '%s'", icmpConfig.Interface)
	}

	if !icmpConfig.Enabled {
		t.Error("Expected enabled true, got false")
	}

	if icmpConfig.TTL != 64 {
		t.Errorf("Expected TTL 64, got %d", icmpConfig.TTL)
	}

	if icmpConfig.MaxSize != 1500 {
		t.Errorf("Expected MaxSize 1500, got %d", icmpConfig.MaxSize)
	}
}
