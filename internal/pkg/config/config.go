package config

import (
	"fmt"
	"os"
	"path/filepath"
	"strings"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
	"go.uber.org/zap"

	"talusaegis/internal/pkg/ebpf"
)

// Config represents the application configuration
type Config struct {
	Server ServerConfig `json:"server" yaml:"server"`
	ICMP   ICMPConfig   `json:"icmp" yaml:"icmp"`
	Log    LogConfig    `json:"log" yaml:"log"`
}

// ServerConfig represents server configuration
type ServerConfig struct {
	Host         string `json:"host" yaml:"host"`
	Port         int    `json:"port" yaml:"port"`
	ReadTimeout  int    `json:"read_timeout" yaml:"read_timeout"`
	WriteTimeout int    `json:"write_timeout" yaml:"write_timeout"`
}

// ICMPConfig represents ICMP responder configuration
type ICMPConfig struct {
	Interface   string `json:"interface" yaml:"interface"`
	Enabled     bool   `json:"enabled" yaml:"enabled"`
	TTL         uint32 `json:"ttl" yaml:"ttl"`
	MaxSize     uint32 `json:"max_size" yaml:"max_size"`
	Debug       bool   `json:"debug" yaml:"debug"`
	UseSourceIP bool   `json:"use_source_ip" yaml:"use_source_ip"` // If true, use interface IP as source; if false, use original destination IP (IP spoofing)
}

// LogConfig represents logging configuration
type LogConfig struct {
	Level      string `json:"level" yaml:"level"`
	Format     string `json:"format" yaml:"format"`
	Output     string `json:"output" yaml:"output"`
	MaxSize    int    `json:"max_size" yaml:"max_size"`
	MaxBackups int    `json:"max_backups" yaml:"max_backups"`
	MaxAge     int    `json:"max_age" yaml:"max_age"`
	Compress   bool   `json:"compress" yaml:"compress"`
}

// Manager manages application configuration
type Manager struct {
	config *Config
	logger *zap.Logger
	viper  *viper.Viper
}

// NewManager creates a new configuration manager
func NewManager(logger *zap.Logger) *Manager {
	return &Manager{
		logger: logger,
		viper:  viper.New(),
	}
}

// Load loads configuration from file and environment variables
func (m *Manager) Load(configPath string) error {
	// Set default values
	m.setDefaults()

	// Configure viper
	m.viper.SetConfigType("yaml")
	m.viper.SetEnvPrefix("TALUSAEGIS")
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	m.viper.AutomaticEnv()

	// Load from file if provided
	if configPath != "" {
		if _, err := os.Stat(configPath); err == nil {
			m.viper.SetConfigFile(configPath)
			if err := m.viper.ReadInConfig(); err != nil {
				return fmt.Errorf("failed to read config file: %w", err)
			}
			m.logger.Info("Configuration loaded from file", zap.String("path", configPath))
		} else {
			m.logger.Warn("Config file not found, using defaults", zap.String("path", configPath))
		}
	} else {
		// Try to find config file in common locations
		m.viper.SetConfigName("config")
		m.viper.AddConfigPath(".")
		m.viper.AddConfigPath("./configs")
		m.viper.AddConfigPath("/etc/talusaegis")
		m.viper.AddConfigPath("$HOME/.talusaegis")

		if err := m.viper.ReadInConfig(); err != nil {
			if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
				return fmt.Errorf("failed to read config: %w", err)
			}
			m.logger.Info("No config file found, using defaults and environment variables")
		} else {
			m.logger.Info("Configuration loaded", zap.String("file", m.viper.ConfigFileUsed()))
		}
	}

	// Unmarshal configuration
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// Handle uint32 fields that viper might not unmarshal correctly
	if config.ICMP.TTL == 0 {
		config.ICMP.TTL = uint32(m.viper.GetInt("icmp.ttl"))
	}
	if config.ICMP.MaxSize == 0 {
		config.ICMP.MaxSize = uint32(m.viper.GetInt("icmp.max_size"))
	}

	// Validate configuration
	if err := m.validate(config); err != nil {
		return fmt.Errorf("invalid configuration: %w", err)
	}

	m.config = config
	return nil
}

// setDefaults sets default configuration values
func (m *Manager) setDefaults() {
	// Server defaults
	m.viper.SetDefault("server.host", "0.0.0.0")
	m.viper.SetDefault("server.port", 8080)
	m.viper.SetDefault("server.read_timeout", 30)
	m.viper.SetDefault("server.write_timeout", 30)

	// ICMP defaults
	m.viper.SetDefault("icmp.interface", "eth0")
	m.viper.SetDefault("icmp.enabled", true)
	m.viper.SetDefault("icmp.ttl", uint32(64))
	m.viper.SetDefault("icmp.max_size", uint32(1500))
	m.viper.SetDefault("icmp.use_source_ip", true) // Default to using interface IP for better compatibility

	// Log defaults
	m.viper.SetDefault("log.level", "info")
	m.viper.SetDefault("log.format", "json")
	m.viper.SetDefault("log.output", "stdout")
	m.viper.SetDefault("log.max_size", 100)
	m.viper.SetDefault("log.max_backups", 3)
	m.viper.SetDefault("log.max_age", 28)
	m.viper.SetDefault("log.compress", true)
}

// validate validates the configuration
func (m *Manager) validate(config *Config) error {
	// Validate server configuration
	if config.Server.Port < 1 || config.Server.Port > 65535 {
		return fmt.Errorf("invalid server port: %d", config.Server.Port)
	}

	// Validate ICMP configuration
	if config.ICMP.Interface == "" {
		return fmt.Errorf("ICMP interface cannot be empty")
	}

	if config.ICMP.TTL == 0 || config.ICMP.TTL > 255 {
		return fmt.Errorf("invalid TTL value: %d", config.ICMP.TTL)
	}

	if config.ICMP.MaxSize == 0 || config.ICMP.MaxSize > 65535 {
		return fmt.Errorf("invalid max size: %d", config.ICMP.MaxSize)
	}

	// Validate log configuration
	validLevels := []string{"debug", "info", "warn", "error", "fatal"}
	levelValid := false
	for _, level := range validLevels {
		if config.Log.Level == level {
			levelValid = true
			break
		}
	}
	if !levelValid {
		return fmt.Errorf("invalid log level: %s", config.Log.Level)
	}

	validFormats := []string{"json", "console"}
	formatValid := false
	for _, format := range validFormats {
		if config.Log.Format == format {
			formatValid = true
			break
		}
	}
	if !formatValid {
		return fmt.Errorf("invalid log format: %s", config.Log.Format)
	}

	return nil
}

// Get returns the current configuration
func (m *Manager) Get() *Config {
	return m.config
}

// GetICMPConfig returns ICMP configuration for eBPF loader
func (m *Manager) GetICMPConfig() *ebpf.Config {
	if m.config == nil {
		return nil
	}

	return &ebpf.Config{
		Interface:   m.config.ICMP.Interface,
		Enabled:     m.config.ICMP.Enabled,
		TTL:         m.config.ICMP.TTL,
		MaxSize:     m.config.ICMP.MaxSize,
		Debug:       m.config.ICMP.Debug,
		UseSourceIP: m.config.ICMP.UseSourceIP,
	}
}

// Save saves the current configuration to file
func (m *Manager) Save(configPath string) error {
	if m.config == nil {
		return fmt.Errorf("no configuration to save")
	}

	// Ensure directory exists
	dir := filepath.Dir(configPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("failed to create config directory: %w", err)
	}

	// Write configuration to file
	if err := m.viper.WriteConfigAs(configPath); err != nil {
		return fmt.Errorf("failed to write config file: %w", err)
	}

	m.logger.Info("Configuration saved", zap.String("path", configPath))
	return nil
}

// Watch watches for configuration file changes
func (m *Manager) Watch(callback func(*Config)) error {
	m.viper.WatchConfig()
	m.viper.OnConfigChange(func(e fsnotify.Event) {
		m.logger.Info("Configuration file changed", zap.String("file", e.Name))

		// Reload configuration
		config := &Config{}
		if err := m.viper.Unmarshal(config); err != nil {
			m.logger.Error("Failed to reload configuration", zap.Error(err))
			return
		}

		// Validate configuration
		if err := m.validate(config); err != nil {
			m.logger.Error("Invalid configuration after reload", zap.Error(err))
			return
		}

		m.config = config
		if callback != nil {
			callback(config)
		}
	})

	return nil
}
